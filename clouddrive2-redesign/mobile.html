<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="CloudDrive2">
    <title>CloudDrive2 - 移动端</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="./styles/main.css" rel="stylesheet">
    <link href="./styles/liquid-glass.css" rel="stylesheet">
    <link href="./styles/mobile.css" rel="stylesheet">
</head>
<body class="bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 min-h-screen">
    <!-- 移动端主容器 -->
    <div id="mobileApp" class="flex flex-col h-screen">
        <!-- 顶部状态栏 -->
        <div class="status-bar">
            <div class="status-left">
                <span class="time">9:41</span>
            </div>
            <div class="status-center">
                <div class="notch"></div>
            </div>
            <div class="status-right">
                <i class="fas fa-signal"></i>
                <i class="fas fa-wifi"></i>
                <i class="fas fa-battery-three-quarters"></i>
            </div>
        </div>

        <!-- 头部导航 -->
        <header class="mobile-header">
            <div class="flex items-center justify-between px-4 py-3">
                <button class="menu-btn">
                    <i class="fas fa-bars"></i>
                </button>
                <h1 class="header-title">我的云盘</h1>
                <button class="search-btn">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        </header>

        <!-- 快速操作栏 -->
        <div class="quick-actions">
            <div class="flex space-x-3 px-4 py-3 overflow-x-auto">
                <button class="quick-action-btn">
                    <i class="fas fa-plus"></i>
                    <span>新建</span>
                </button>
                <button class="quick-action-btn">
                    <i class="fas fa-upload"></i>
                    <span>上传</span>
                </button>
                <button class="quick-action-btn">
                    <i class="fas fa-camera"></i>
                    <span>拍照</span>
                </button>
                <button class="quick-action-btn">
                    <i class="fas fa-folder"></i>
                    <span>文件夹</span>
                </button>
                <button class="quick-action-btn">
                    <i class="fas fa-share"></i>
                    <span>分享</span>
                </button>
            </div>
        </div>

        <!-- 云盘状态卡片 -->
        <div class="cloud-status-cards">
            <div class="flex space-x-3 px-4 py-2 overflow-x-auto">
                <div class="cloud-status-card">
                    <div class="flex items-center space-x-2">
                        <div class="cloud-icon aliyun">阿</div>
                        <div>
                            <h4 class="text-sm font-medium">阿里云盘</h4>
                            <p class="text-xs text-gray-500">2.3TB / 5TB</p>
                        </div>
                    </div>
                    <div class="status-indicator online"></div>
                </div>
                
                <div class="cloud-status-card">
                    <div class="flex items-center space-x-2">
                        <div class="cloud-icon baidu">百</div>
                        <div>
                            <h4 class="text-sm font-medium">百度网盘</h4>
                            <p class="text-xs text-gray-500">1.8TB / 2TB</p>
                        </div>
                    </div>
                    <div class="status-indicator online"></div>
                </div>
                
                <div class="cloud-status-card">
                    <div class="flex items-center space-x-2">
                        <div class="cloud-icon onedrive">O</div>
                        <div>
                            <h4 class="text-sm font-medium">OneDrive</h4>
                            <p class="text-xs text-gray-500">离线</p>
                        </div>
                    </div>
                    <div class="status-indicator offline"></div>
                </div>
            </div>
        </div>

        <!-- 主内容区 -->
        <main class="flex-1 overflow-hidden">
            <!-- 文件列表 -->
            <div class="file-list-mobile">
                <!-- 文件夹 -->
                <div class="mobile-file-item folder" data-type="folder">
                    <div class="file-icon-mobile">
                        <i class="fas fa-folder"></i>
                    </div>
                    <div class="file-info-mobile">
                        <h4 class="file-name-mobile">工作文档</h4>
                        <p class="file-meta-mobile">15个文件 • 今天</p>
                    </div>
                    <button class="file-menu-btn">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                </div>

                <!-- 图片文件 -->
                <div class="mobile-file-item image" data-type="image">
                    <div class="file-icon-mobile">
                        <i class="fas fa-image"></i>
                    </div>
                    <div class="file-info-mobile">
                        <h4 class="file-name-mobile">设计稿.png</h4>
                        <p class="file-meta-mobile">2.3 MB • 昨天</p>
                    </div>
                    <button class="file-menu-btn">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                </div>

                <!-- 视频文件 -->
                <div class="mobile-file-item video" data-type="video">
                    <div class="file-icon-mobile">
                        <i class="fas fa-play"></i>
                    </div>
                    <div class="file-info-mobile">
                        <h4 class="file-name-mobile">演示视频.mp4</h4>
                        <p class="file-meta-mobile">156 MB • 2天前</p>
                    </div>
                    <button class="file-menu-btn">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                </div>

                <!-- 文档文件 -->
                <div class="mobile-file-item document" data-type="document">
                    <div class="file-icon-mobile">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <div class="file-info-mobile">
                        <h4 class="file-name-mobile">项目计划.docx</h4>
                        <p class="file-meta-mobile">1.2 MB • 1周前</p>
                    </div>
                    <button class="file-menu-btn">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                </div>

                <!-- 音频文件 -->
                <div class="mobile-file-item audio" data-type="audio">
                    <div class="file-icon-mobile">
                        <i class="fas fa-music"></i>
                    </div>
                    <div class="file-info-mobile">
                        <h4 class="file-name-mobile">背景音乐.mp3</h4>
                        <p class="file-meta-mobile">5.8 MB • 2周前</p>
                    </div>
                    <button class="file-menu-btn">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                </div>

                <!-- 压缩文件 -->
                <div class="mobile-file-item archive" data-type="archive">
                    <div class="file-icon-mobile">
                        <i class="fas fa-file-archive"></i>
                    </div>
                    <div class="file-info-mobile">
                        <h4 class="file-name-mobile">项目源码.zip</h4>
                        <p class="file-meta-mobile">45.2 MB • 1个月前</p>
                    </div>
                    <button class="file-menu-btn">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                </div>
            </div>
        </main>

        <!-- 底部导航 -->
        <nav class="mobile-bottom-nav">
            <a href="#" class="nav-item-mobile active">
                <i class="fas fa-home"></i>
                <span>首页</span>
            </a>
            <a href="#" class="nav-item-mobile">
                <i class="fas fa-folder"></i>
                <span>文件</span>
            </a>
            <a href="#" class="nav-item-mobile">
                <i class="fas fa-cloud"></i>
                <span>云盘</span>
            </a>
            <a href="#" class="nav-item-mobile">
                <i class="fas fa-star"></i>
                <span>收藏</span>
            </a>
            <a href="#" class="nav-item-mobile">
                <i class="fas fa-user"></i>
                <span>我的</span>
            </a>
        </nav>
    </div>

    <!-- 侧边菜单 -->
    <div class="mobile-sidebar" id="mobileSidebar">
        <div class="sidebar-header">
            <div class="user-info">
                <div class="user-avatar">
                    <i class="fas fa-user"></i>
                </div>
                <div>
                    <h3 class="user-name">用户名</h3>
                    <p class="user-email"><EMAIL></p>
                </div>
            </div>
            <button class="close-sidebar-btn">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <div class="sidebar-content">
            <nav class="sidebar-nav">
                <a href="#" class="sidebar-nav-item">
                    <i class="fas fa-home"></i>
                    <span>首页</span>
                </a>
                <a href="#" class="sidebar-nav-item">
                    <i class="fas fa-star"></i>
                    <span>收藏夹</span>
                </a>
                <a href="#" class="sidebar-nav-item">
                    <i class="fas fa-clock"></i>
                    <span>最近访问</span>
                </a>
                <a href="#" class="sidebar-nav-item">
                    <i class="fas fa-download"></i>
                    <span>下载管理</span>
                </a>
                <a href="#" class="sidebar-nav-item">
                    <i class="fas fa-trash"></i>
                    <span>回收站</span>
                </a>
                <a href="#" class="sidebar-nav-item">
                    <i class="fas fa-cog"></i>
                    <span>设置</span>
                </a>
            </nav>
            
            <div class="storage-info">
                <h4>存储空间</h4>
                <div class="storage-bar-mobile">
                    <div class="storage-progress-mobile" style="width: 68%"></div>
                </div>
                <p class="storage-text">已使用 3.4TB / 5TB</p>
            </div>
        </div>
    </div>

    <!-- 搜索覆盖层 -->
    <div class="search-overlay" id="searchOverlay">
        <div class="search-header">
            <button class="back-btn">
                <i class="fas fa-arrow-left"></i>
            </button>
            <div class="search-input-container">
                <i class="fas fa-search"></i>
                <input type="text" placeholder="搜索文件..." class="search-input-mobile">
            </div>
        </div>
        
        <div class="search-suggestions">
            <h4>最近搜索</h4>
            <div class="suggestion-item">
                <i class="fas fa-clock"></i>
                <span>项目文档</span>
            </div>
            <div class="suggestion-item">
                <i class="fas fa-clock"></i>
                <span>设计稿</span>
            </div>
        </div>
    </div>

    <!-- 文件操作菜单 -->
    <div class="file-action-menu" id="fileActionMenu">
        <div class="action-menu-header">
            <h3>文件操作</h3>
            <button class="close-menu-btn">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="action-menu-content">
            <button class="action-menu-item">
                <i class="fas fa-eye"></i>
                <span>预览</span>
            </button>
            <button class="action-menu-item">
                <i class="fas fa-download"></i>
                <span>下载</span>
            </button>
            <button class="action-menu-item">
                <i class="fas fa-share"></i>
                <span>分享</span>
            </button>
            <button class="action-menu-item">
                <i class="fas fa-star"></i>
                <span>收藏</span>
            </button>
            <button class="action-menu-item">
                <i class="fas fa-edit"></i>
                <span>重命名</span>
            </button>
            <button class="action-menu-item">
                <i class="fas fa-copy"></i>
                <span>复制</span>
            </button>
            <button class="action-menu-item">
                <i class="fas fa-cut"></i>
                <span>剪切</span>
            </button>
            <button class="action-menu-item danger">
                <i class="fas fa-trash"></i>
                <span>删除</span>
            </button>
        </div>
    </div>

    <!-- 遮罩层 -->
    <div class="overlay" id="overlay"></div>

    <script src="./js/liquid-glass.js"></script>
    <script src="./js/mobile.js"></script>
</body>
</html>
