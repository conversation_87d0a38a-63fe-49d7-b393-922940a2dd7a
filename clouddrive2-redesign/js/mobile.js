// 移动端CloudDrive2交互脚本

class MobileCloudDriveUI {
  constructor() {
    this.init();
    this.bindEvents();
    this.initGestures();
    this.updateTime();
  }

  init() {
    // 初始化状态
    this.sidebarOpen = false;
    this.searchOpen = false;
    this.actionMenuOpen = false;
    this.selectedFiles = new Set();
    
    // 获取DOM元素
    this.sidebar = document.getElementById('mobileSidebar');
    this.searchOverlay = document.getElementById('searchOverlay');
    this.actionMenu = document.getElementById('fileActionMenu');
    this.overlay = document.getElementById('overlay');
    
    // 检测设备特性
    this.isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
    this.hasNotch = this.isIOS && window.screen.height >= 812;
    
    // 应用设备特定样式
    this.applyDeviceStyles();
    
    // 初始化液态玻璃效果
    this.initMobileLiquidEffects();
  }

  bindEvents() {
    // 菜单按钮
    document.querySelector('.menu-btn').addEventListener('click', () => {
      this.toggleSidebar();
    });

    // 搜索按钮
    document.querySelector('.search-btn').addEventListener('click', () => {
      this.openSearch();
    });

    // 关闭侧边栏
    document.querySelector('.close-sidebar-btn').addEventListener('click', () => {
      this.closeSidebar();
    });

    // 返回按钮（搜索）
    document.querySelector('.back-btn').addEventListener('click', () => {
      this.closeSearch();
    });

    // 文件项点击
    document.querySelectorAll('.mobile-file-item').forEach(item => {
      item.addEventListener('click', (e) => this.handleFileClick(e, item));
    });

    // 文件菜单按钮
    document.querySelectorAll('.file-menu-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        e.stopPropagation();
        this.showActionMenu();
      });
    });

    // 关闭操作菜单
    document.querySelector('.close-menu-btn').addEventListener('click', () => {
      this.closeActionMenu();
    });

    // 遮罩层点击
    this.overlay.addEventListener('click', () => {
      this.closeAllOverlays();
    });

    // 快速操作按钮
    document.querySelectorAll('.quick-action-btn').forEach(btn => {
      btn.addEventListener('click', (e) => this.handleQuickAction(e, btn));
    });

    // 底部导航
    document.querySelectorAll('.nav-item-mobile').forEach(item => {
      item.addEventListener('click', (e) => this.handleNavClick(e, item));
    });

    // 侧边栏导航
    document.querySelectorAll('.sidebar-nav-item').forEach(item => {
      item.addEventListener('click', (e) => this.handleSidebarNavClick(e, item));
    });

    // 操作菜单项
    document.querySelectorAll('.action-menu-item').forEach(item => {
      item.addEventListener('click', (e) => this.handleActionMenuClick(e, item));
    });

    // 搜索输入
    const searchInput = document.querySelector('.search-input-mobile');
    if (searchInput) {
      searchInput.addEventListener('input', (e) => this.handleSearch(e.target.value));
    }

    // 键盘事件
    document.addEventListener('keydown', (e) => this.handleKeyboard(e));

    // 设备方向变化
    window.addEventListener('orientationchange', () => {
      setTimeout(() => this.handleOrientationChange(), 100);
    });

    // 窗口大小变化
    window.addEventListener('resize', () => this.handleResize());
  }

  initGestures() {
    // 侧边栏滑动手势
    let startX = 0;
    let currentX = 0;
    let isDragging = false;

    document.addEventListener('touchstart', (e) => {
      startX = e.touches[0].clientX;
      if (startX < 20 || this.sidebarOpen) {
        isDragging = true;
      }
    });

    document.addEventListener('touchmove', (e) => {
      if (!isDragging) return;
      
      currentX = e.touches[0].clientX;
      const deltaX = currentX - startX;
      
      if (this.sidebarOpen) {
        // 关闭手势
        if (deltaX < -50) {
          this.sidebar.style.transform = `translateX(${Math.max(deltaX, -280)}px)`;
        }
      } else {
        // 打开手势
        if (deltaX > 50 && startX < 20) {
          this.sidebar.style.transform = `translateX(${Math.min(deltaX - 280, 0)}px)`;
          this.sidebar.style.left = '0';
        }
      }
    });

    document.addEventListener('touchend', (e) => {
      if (!isDragging) return;
      
      const deltaX = currentX - startX;
      
      if (this.sidebarOpen && deltaX < -100) {
        this.closeSidebar();
      } else if (!this.sidebarOpen && deltaX > 100 && startX < 20) {
        this.openSidebar();
      } else {
        // 重置位置
        this.sidebar.style.transform = '';
        if (!this.sidebarOpen) {
          this.sidebar.style.left = '-100%';
        }
      }
      
      isDragging = false;
    });

    // 下拉刷新手势
    let startY = 0;
    let pullDistance = 0;
    const fileList = document.querySelector('.file-list-mobile');
    
    fileList.addEventListener('touchstart', (e) => {
      if (fileList.scrollTop === 0) {
        startY = e.touches[0].clientY;
      }
    });

    fileList.addEventListener('touchmove', (e) => {
      if (fileList.scrollTop === 0 && startY > 0) {
        pullDistance = e.touches[0].clientY - startY;
        if (pullDistance > 0) {
          e.preventDefault();
          const opacity = Math.min(pullDistance / 100, 1);
          fileList.style.transform = `translateY(${Math.min(pullDistance * 0.5, 50)}px)`;
          // 这里可以添加下拉刷新指示器
        }
      }
    });

    fileList.addEventListener('touchend', () => {
      if (pullDistance > 80) {
        this.refreshFileList();
      }
      fileList.style.transform = '';
      startY = 0;
      pullDistance = 0;
    });
  }

  applyDeviceStyles() {
    if (this.isIOS) {
      document.body.classList.add('ios');
    }
    
    if (this.hasNotch) {
      document.body.classList.add('has-notch');
    }
  }

  initMobileLiquidEffects() {
    // 为移动端元素添加液态效果
    document.querySelectorAll('.mobile-file-item').forEach(item => {
      item.classList.add('liquid-card');
    });

    document.querySelectorAll('.quick-action-btn').forEach(btn => {
      btn.classList.add('liquid-button');
    });

    document.querySelectorAll('.cloud-status-card').forEach(card => {
      card.classList.add('liquid-glass');
    });

    // 添加触摸反馈
    this.addTouchFeedback();
  }

  addTouchFeedback() {
    const touchElements = document.querySelectorAll(
      '.mobile-file-item, .quick-action-btn, .nav-item-mobile, .action-menu-item'
    );

    touchElements.forEach(element => {
      element.addEventListener('touchstart', () => {
        element.style.transform = 'scale(0.98)';
        element.style.opacity = '0.8';
      });

      element.addEventListener('touchend', () => {
        setTimeout(() => {
          element.style.transform = '';
          element.style.opacity = '';
        }, 100);
      });
    });
  }

  toggleSidebar() {
    if (this.sidebarOpen) {
      this.closeSidebar();
    } else {
      this.openSidebar();
    }
  }

  openSidebar() {
    this.sidebarOpen = true;
    this.sidebar.classList.add('open');
    this.overlay.classList.add('show');
    document.body.style.overflow = 'hidden';
  }

  closeSidebar() {
    this.sidebarOpen = false;
    this.sidebar.classList.remove('open');
    this.overlay.classList.remove('show');
    document.body.style.overflow = '';
  }

  openSearch() {
    this.searchOpen = true;
    this.searchOverlay.classList.add('open');
    // 自动聚焦搜索框
    setTimeout(() => {
      document.querySelector('.search-input-mobile').focus();
    }, 300);
  }

  closeSearch() {
    this.searchOpen = false;
    this.searchOverlay.classList.remove('open');
  }

  showActionMenu() {
    this.actionMenuOpen = true;
    this.actionMenu.classList.add('open');
    this.overlay.classList.add('show');
    document.body.style.overflow = 'hidden';
  }

  closeActionMenu() {
    this.actionMenuOpen = false;
    this.actionMenu.classList.remove('open');
    this.overlay.classList.remove('show');
    document.body.style.overflow = '';
  }

  closeAllOverlays() {
    this.closeSidebar();
    this.closeSearch();
    this.closeActionMenu();
  }

  handleFileClick(e, item) {
    // 添加点击动画
    item.style.transform = 'scale(0.98)';
    setTimeout(() => {
      item.style.transform = '';
    }, 150);

    const fileType = item.dataset.type;
    const fileName = item.querySelector('.file-name-mobile').textContent;
    
    console.log(`Opening ${fileType}: ${fileName}`);
    
    // 这里可以添加文件打开逻辑
    if (fileType === 'folder') {
      this.openFolder(fileName);
    } else {
      this.openFile(fileName, fileType);
    }
  }

  handleQuickAction(e, btn) {
    const action = btn.querySelector('span').textContent;
    
    // 添加点击反馈
    btn.style.transform = 'scale(0.95)';
    setTimeout(() => {
      btn.style.transform = '';
    }, 150);

    switch (action) {
      case '新建':
        this.showCreateMenu();
        break;
      case '上传':
        this.showUploadOptions();
        break;
      case '拍照':
        this.openCamera();
        break;
      case '文件夹':
        this.createFolder();
        break;
      case '分享':
        this.showShareOptions();
        break;
    }
  }

  handleNavClick(e, item) {
    e.preventDefault();
    
    // 更新导航状态
    document.querySelectorAll('.nav-item-mobile').forEach(nav => {
      nav.classList.remove('active');
    });
    item.classList.add('active');

    const navText = item.querySelector('span').textContent;
    console.log(`Navigating to: ${navText}`);
  }

  handleSidebarNavClick(e, item) {
    e.preventDefault();
    
    const navText = item.querySelector('span').textContent;
    console.log(`Sidebar navigation: ${navText}`);
    
    this.closeSidebar();
  }

  handleActionMenuClick(e, item) {
    const action = item.querySelector('span').textContent;
    console.log(`File action: ${action}`);
    
    this.closeActionMenu();
    
    // 执行相应操作
    switch (action) {
      case '预览':
        this.previewFile();
        break;
      case '下载':
        this.downloadFile();
        break;
      case '分享':
        this.shareFile();
        break;
      case '收藏':
        this.favoriteFile();
        break;
      case '重命名':
        this.renameFile();
        break;
      case '删除':
        this.deleteFile();
        break;
    }
  }

  handleSearch(query) {
    console.log(`Searching for: ${query}`);
    // 实现搜索逻辑
  }

  handleKeyboard(e) {
    // ESC键关闭覆盖层
    if (e.key === 'Escape') {
      this.closeAllOverlays();
    }
  }

  handleOrientationChange() {
    // 处理设备方向变化
    this.closeAllOverlays();
  }

  handleResize() {
    // 处理窗口大小变化
    this.closeAllOverlays();
  }

  updateTime() {
    const timeElement = document.querySelector('.time');
    if (timeElement) {
      const now = new Date();
      const hours = now.getHours().toString().padStart(2, '0');
      const minutes = now.getMinutes().toString().padStart(2, '0');
      timeElement.textContent = `${hours}:${minutes}`;
    }
    
    // 每分钟更新一次
    setTimeout(() => this.updateTime(), 60000);
  }

  refreshFileList() {
    console.log('Refreshing file list...');
    // 实现下拉刷新逻辑
    
    // 模拟加载
    setTimeout(() => {
      console.log('File list refreshed');
    }, 1000);
  }

  // 文件操作方法
  openFolder(folderName) {
    console.log(`Opening folder: ${folderName}`);
  }

  openFile(fileName, fileType) {
    console.log(`Opening file: ${fileName} (${fileType})`);
  }

  showCreateMenu() {
    console.log('Showing create menu');
  }

  showUploadOptions() {
    console.log('Showing upload options');
  }

  openCamera() {
    console.log('Opening camera');
  }

  createFolder() {
    console.log('Creating folder');
  }

  showShareOptions() {
    console.log('Showing share options');
  }

  previewFile() {
    console.log('Previewing file');
  }

  downloadFile() {
    console.log('Downloading file');
  }

  shareFile() {
    console.log('Sharing file');
  }

  favoriteFile() {
    console.log('Adding to favorites');
  }

  renameFile() {
    console.log('Renaming file');
  }

  deleteFile() {
    console.log('Deleting file');
  }
}

// 初始化移动端应用
document.addEventListener('DOMContentLoaded', () => {
  new MobileCloudDriveUI();
});

// 防止双击缩放
document.addEventListener('touchstart', function(event) {
  if (event.touches.length > 1) {
    event.preventDefault();
  }
});

let lastTouchEnd = 0;
document.addEventListener('touchend', function(event) {
  const now = (new Date()).getTime();
  if (now - lastTouchEnd <= 300) {
    event.preventDefault();
  }
  lastTouchEnd = now;
}, false);

// PWA支持
if ('serviceWorker' in navigator) {
  window.addEventListener('load', () => {
    navigator.serviceWorker.register('./sw.js')
      .then(registration => {
        console.log('SW registered: ', registration);
      })
      .catch(registrationError => {
        console.log('SW registration failed: ', registrationError);
      });
  });
}
