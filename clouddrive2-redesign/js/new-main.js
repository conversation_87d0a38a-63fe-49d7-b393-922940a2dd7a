// CloudDrive 现代化UI交互脚本

class CloudDriveApp {
  constructor() {
    this.selectedFiles = new Set();
    this.currentView = 'list';
    this.contextMenu = document.getElementById('contextMenu');
    this.init();
  }

  init() {
    this.bindEvents();
    this.initLiquidEffects();
  }

  bindEvents() {
    // 视图切换
    document.querySelectorAll('.view-btn').forEach(btn => {
      btn.addEventListener('click', (e) => this.switchView(e.target.dataset.view));
    });

    // 文件项点击
    document.querySelectorAll('.file-item').forEach(item => {
      item.addEventListener('click', (e) => this.handleFileClick(e, item));
      item.addEventListener('contextmenu', (e) => this.showContextMenu(e, item));
      item.addEventListener('dblclick', (e) => this.openFile(item));
    });

    // 搜索功能
    const searchInput = document.querySelector('.search-input');
    if (searchInput) {
      searchInput.addEventListener('input', this.debounce((e) => {
        this.handleSearch(e.target.value);
      }, 300));
    }

    // 导航项点击
    document.querySelectorAll('.nav-link').forEach(link => {
      link.addEventListener('click', (e) => this.handleNavClick(e, link));
    });

    // 操作按钮
    document.querySelectorAll('.action-btn').forEach(btn => {
      btn.addEventListener('click', (e) => this.handleActionClick(e, btn));
    });

    // 右键菜单
    document.addEventListener('click', (e) => {
      if (!this.contextMenu.contains(e.target)) {
        this.hideContextMenu();
      }
    });

    // 右键菜单项
    document.querySelectorAll('.context-menu-item').forEach(item => {
      item.addEventListener('click', (e) => this.handleContextMenuClick(e, item));
    });

    // 键盘快捷键
    document.addEventListener('keydown', (e) => this.handleKeyboard(e));

    // 文件拖拽
    this.initDragAndDrop();
  }

  switchView(view) {
    if (view === this.currentView) return;
    
    this.currentView = view;
    
    // 更新按钮状态
    document.querySelectorAll('.view-btn').forEach(btn => {
      btn.classList.toggle('active', btn.dataset.view === view);
    });

    // 切换视图样式
    const fileList = document.querySelector('.file-list');
    if (view === 'grid') {
      fileList.classList.add('grid-view');
    } else {
      fileList.classList.remove('grid-view');
    }

    // 添加切换动画
    fileList.style.opacity = '0.7';
    setTimeout(() => {
      fileList.style.opacity = '1';
    }, 150);
  }

  handleFileClick(e, item) {
    // 处理多选
    if (e.ctrlKey || e.metaKey) {
      this.toggleFileSelection(item);
    } else if (e.shiftKey && this.selectedFiles.size > 0) {
      this.selectFileRange(item);
    } else {
      this.selectSingleFile(item);
    }
  }

  toggleFileSelection(item) {
    if (this.selectedFiles.has(item)) {
      this.selectedFiles.delete(item);
      item.classList.remove('selected');
    } else {
      this.selectedFiles.add(item);
      item.classList.add('selected');
    }
  }

  selectSingleFile(item) {
    // 清除之前的选择
    this.selectedFiles.forEach(file => file.classList.remove('selected'));
    this.selectedFiles.clear();
    
    // 选择当前文件
    this.selectedFiles.add(item);
    item.classList.add('selected');
  }

  selectFileRange(endItem) {
    const fileItems = Array.from(document.querySelectorAll('.file-item'));
    const lastSelected = Array.from(this.selectedFiles).pop();
    
    if (!lastSelected) {
      this.selectSingleFile(endItem);
      return;
    }

    const startIndex = fileItems.indexOf(lastSelected);
    const endIndex = fileItems.indexOf(endItem);
    const [min, max] = [Math.min(startIndex, endIndex), Math.max(startIndex, endIndex)];

    // 清除当前选择
    this.selectedFiles.forEach(file => file.classList.remove('selected'));
    this.selectedFiles.clear();

    // 选择范围内的文件
    for (let i = min; i <= max; i++) {
      const item = fileItems[i];
      this.selectedFiles.add(item);
      item.classList.add('selected');
    }
  }

  openFile(item) {
    const fileName = item.querySelector('.file-name').textContent;
    const fileType = item.dataset.type;
    
    console.log(`Opening ${fileType}: ${fileName}`);
    
    // 添加打开动画
    item.style.transform = 'scale(0.98)';
    setTimeout(() => {
      item.style.transform = '';
    }, 150);

    // 这里可以添加实际的文件打开逻辑
    if (fileType === 'folder') {
      this.navigateToFolder(fileName);
    }
  }

  navigateToFolder(folderName) {
    // 更新面包屑
    const breadcrumb = document.querySelector('.breadcrumb');
    const separator = document.createElement('i');
    separator.className = 'fas fa-chevron-right breadcrumb-separator';
    const newItem = document.createElement('span');
    newItem.className = 'breadcrumb-current';
    newItem.textContent = folderName;
    
    // 将当前项改为链接
    const currentItem = breadcrumb.querySelector('.breadcrumb-current');
    if (currentItem) {
      const link = document.createElement('a');
      link.href = '#';
      link.className = 'breadcrumb-item';
      link.textContent = currentItem.textContent;
      currentItem.replaceWith(link);
    }
    
    breadcrumb.appendChild(separator);
    breadcrumb.appendChild(newItem);
  }

  showContextMenu(e, item) {
    e.preventDefault();
    
    if (!this.selectedFiles.has(item)) {
      this.selectSingleFile(item);
    }

    const menu = this.contextMenu;
    menu.style.display = 'block';
    menu.style.left = e.pageX + 'px';
    menu.style.top = e.pageY + 'px';

    // 确保菜单不会超出屏幕
    const rect = menu.getBoundingClientRect();
    if (rect.right > window.innerWidth) {
      menu.style.left = (e.pageX - rect.width) + 'px';
    }
    if (rect.bottom > window.innerHeight) {
      menu.style.top = (e.pageY - rect.height) + 'px';
    }
  }

  hideContextMenu() {
    this.contextMenu.style.display = 'none';
  }

  handleContextMenuClick(e, item) {
    const action = item.querySelector('span').textContent;
    console.log(`Context menu action: ${action}`);
    
    switch (action) {
      case '打开':
        this.openSelectedFiles();
        break;
      case '下载':
        this.downloadSelectedFiles();
        break;
      case '分享':
        this.shareSelectedFiles();
        break;
      case '重命名':
        this.renameSelectedFile();
        break;
      case '删除':
        this.deleteSelectedFiles();
        break;
    }
    
    this.hideContextMenu();
  }

  handleSearch(query) {
    const fileItems = document.querySelectorAll('.file-item');
    
    fileItems.forEach(item => {
      const fileName = item.querySelector('.file-name').textContent.toLowerCase();
      const matches = fileName.includes(query.toLowerCase());
      
      item.style.display = matches ? 'flex' : 'none';
      
      if (matches && query) {
        // 高亮搜索结果
        this.highlightSearchTerm(item.querySelector('.file-name'), query);
      } else {
        this.removeHighlight(item.querySelector('.file-name'));
      }
    });
  }

  highlightSearchTerm(element, term) {
    const text = element.textContent;
    const regex = new RegExp(`(${term})`, 'gi');
    const highlightedText = text.replace(regex, '<mark>$1</mark>');
    element.innerHTML = highlightedText;
  }

  removeHighlight(element) {
    element.innerHTML = element.textContent;
  }

  handleNavClick(e, link) {
    e.preventDefault();
    
    // 更新导航状态
    document.querySelectorAll('.nav-item').forEach(item => {
      item.classList.remove('active');
    });
    link.closest('.nav-item').classList.add('active');

    // 添加点击动画
    link.style.transform = 'scale(0.98)';
    setTimeout(() => {
      link.style.transform = '';
    }, 100);

    const navText = link.querySelector('span').textContent;
    console.log(`Navigation: ${navText}`);
  }

  handleActionClick(e, btn) {
    const action = btn.textContent.trim();
    
    // 添加点击动画
    btn.style.transform = 'scale(0.95)';
    setTimeout(() => {
      btn.style.transform = '';
    }, 150);

    switch (action) {
      case '新建':
        this.showCreateMenu();
        break;
      case '上传':
        this.showUploadDialog();
        break;
      default:
        console.log(`Action: ${action}`);
    }
  }

  handleKeyboard(e) {
    // Ctrl/Cmd + A: 全选
    if ((e.ctrlKey || e.metaKey) && e.key === 'a') {
      e.preventDefault();
      this.selectAllFiles();
    }
    
    // Delete: 删除选中文件
    if (e.key === 'Delete' && this.selectedFiles.size > 0) {
      this.deleteSelectedFiles();
    }
    
    // Escape: 取消选择
    if (e.key === 'Escape') {
      this.clearSelection();
      this.hideContextMenu();
    }
    
    // Enter: 打开选中文件
    if (e.key === 'Enter' && this.selectedFiles.size === 1) {
      this.openFile(Array.from(this.selectedFiles)[0]);
    }
  }

  selectAllFiles() {
    const visibleFiles = Array.from(document.querySelectorAll('.file-item'))
      .filter(item => item.style.display !== 'none');
    
    this.selectedFiles.clear();
    visibleFiles.forEach(item => {
      this.selectedFiles.add(item);
      item.classList.add('selected');
    });
  }

  clearSelection() {
    this.selectedFiles.forEach(file => file.classList.remove('selected'));
    this.selectedFiles.clear();
  }

  initDragAndDrop() {
    const fileArea = document.querySelector('.file-area');
    
    fileArea.addEventListener('dragover', (e) => {
      e.preventDefault();
      fileArea.classList.add('drag-over');
    });

    fileArea.addEventListener('dragleave', (e) => {
      if (!fileArea.contains(e.relatedTarget)) {
        fileArea.classList.remove('drag-over');
      }
    });

    fileArea.addEventListener('drop', (e) => {
      e.preventDefault();
      fileArea.classList.remove('drag-over');
      
      const files = Array.from(e.dataTransfer.files);
      if (files.length > 0) {
        this.handleFileUpload(files);
      }
    });
  }

  initLiquidEffects() {
    // 为文件项添加液态效果
    document.querySelectorAll('.file-item').forEach(item => {
      item.classList.add('liquid-card');
    });

    // 为按钮添加液态效果
    document.querySelectorAll('.action-btn, .view-btn').forEach(btn => {
      btn.classList.add('liquid-button');
    });

    // 为导航项添加液态效果
    document.querySelectorAll('.nav-link').forEach(link => {
      link.classList.add('liquid-nav-item');
    });
  }

  // 文件操作方法
  openSelectedFiles() {
    this.selectedFiles.forEach(file => this.openFile(file));
  }

  downloadSelectedFiles() {
    console.log(`Downloading ${this.selectedFiles.size} files`);
  }

  shareSelectedFiles() {
    console.log(`Sharing ${this.selectedFiles.size} files`);
  }

  renameSelectedFile() {
    if (this.selectedFiles.size === 1) {
      const file = Array.from(this.selectedFiles)[0];
      const nameElement = file.querySelector('.file-name');
      const currentName = nameElement.textContent;
      
      const input = document.createElement('input');
      input.type = 'text';
      input.value = currentName;
      input.className = 'rename-input';
      
      nameElement.replaceWith(input);
      input.focus();
      input.select();
      
      const finishRename = () => {
        const newName = input.value.trim() || currentName;
        const newNameElement = document.createElement('span');
        newNameElement.className = 'file-name';
        newNameElement.textContent = newName;
        input.replaceWith(newNameElement);
      };
      
      input.addEventListener('blur', finishRename);
      input.addEventListener('keydown', (e) => {
        if (e.key === 'Enter') {
          finishRename();
        } else if (e.key === 'Escape') {
          input.value = currentName;
          finishRename();
        }
      });
    }
  }

  deleteSelectedFiles() {
    if (this.selectedFiles.size > 0) {
      const count = this.selectedFiles.size;
      if (confirm(`确定要删除选中的 ${count} 个项目吗？`)) {
        this.selectedFiles.forEach(file => {
          file.style.animation = 'fadeOut 0.3s ease forwards';
          setTimeout(() => file.remove(), 300);
        });
        this.selectedFiles.clear();
      }
    }
  }

  showCreateMenu() {
    console.log('Showing create menu');
  }

  showUploadDialog() {
    const input = document.createElement('input');
    input.type = 'file';
    input.multiple = true;
    input.addEventListener('change', (e) => {
      this.handleFileUpload(Array.from(e.target.files));
    });
    input.click();
  }

  handleFileUpload(files) {
    console.log(`Uploading ${files.length} files:`, files.map(f => f.name));
  }

  // 工具方法
  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }
}

// 添加CSS动画
const style = document.createElement('style');
style.textContent = `
  @keyframes fadeOut {
    from { opacity: 1; transform: scale(1); }
    to { opacity: 0; transform: scale(0.9); }
  }
  
  .drag-over {
    background: rgba(0, 123, 255, 0.05);
    border: 2px dashed #007bff;
  }
  
  .rename-input {
    background: #ffffff;
    border: 1px solid #007bff;
    border-radius: 4px;
    padding: 2px 6px;
    font-size: 14px;
    outline: none;
    width: 100%;
  }
  
  mark {
    background: #fff3cd;
    padding: 0 2px;
    border-radius: 2px;
  }
  
  .grid-view {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 16px;
    padding: 20px;
  }
  
  .grid-view .file-item {
    flex-direction: column;
    text-align: center;
    padding: 16px;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    background: #ffffff;
  }
  
  .grid-view .file-icon {
    font-size: 32px;
    margin: 0 0 8px 0;
  }
  
  .grid-view .file-col {
    width: 100%;
    justify-content: center;
  }
  
  .grid-view .file-col-size,
  .grid-view .file-col-date {
    display: none;
  }
`;
document.head.appendChild(style);

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
  new CloudDriveApp();
});
