// 移动端CloudDrive现代化UI交互脚本

class MobileCloudDriveApp {
  constructor() {
    this.init();
    this.bindEvents();
    this.initGestures();
    this.updateTime();
  }

  init() {
    // 初始化状态
    this.sidebarOpen = false;
    this.searchOpen = false;
    this.actionSheetOpen = false;
    this.selectedFiles = new Set();
    
    // 获取DOM元素
    this.sidebar = document.getElementById('mobileSidebar');
    this.searchOverlay = document.getElementById('searchOverlay');
    this.actionSheet = document.getElementById('actionSheet');
    
    // 检测设备特性
    this.isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
    this.hasNotch = this.isIOS && window.screen.height >= 812;
    
    // 应用设备特定样式
    this.applyDeviceStyles();
    
    // 初始化液态效果
    this.initLiquidEffects();
  }

  bindEvents() {
    // 菜单按钮
    document.getElementById('menuBtn').addEventListener('click', () => {
      this.toggleSidebar();
    });

    // 搜索按钮
    document.getElementById('searchBtn').addEventListener('click', () => {
      this.openSearch();
    });

    // 关闭侧边栏
    document.getElementById('closeSidebar').addEventListener('click', () => {
      this.closeSidebar();
    });

    // 侧边栏遮罩
    document.getElementById('sidebarOverlay').addEventListener('click', () => {
      this.closeSidebar();
    });

    // 返回按钮（搜索）
    document.getElementById('backBtn').addEventListener('click', () => {
      this.closeSearch();
    });

    // 文件项点击
    document.querySelectorAll('.mobile-file-item').forEach(item => {
      item.addEventListener('click', (e) => this.handleFileClick(e, item));
    });

    // 文件更多按钮
    document.querySelectorAll('.file-more-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        e.stopPropagation();
        this.showActionSheet();
      });
    });

    // 关闭操作菜单
    document.getElementById('closeActionSheet').addEventListener('click', () => {
      this.closeActionSheet();
    });

    // 操作菜单遮罩
    document.getElementById('actionSheetOverlay').addEventListener('click', () => {
      this.closeActionSheet();
    });

    // 快速操作按钮
    document.querySelectorAll('.quick-btn').forEach(btn => {
      btn.addEventListener('click', (e) => this.handleQuickAction(e, btn));
    });

    // 底部导航
    document.querySelectorAll('.nav-tab').forEach(tab => {
      tab.addEventListener('click', (e) => this.handleNavClick(e, tab));
    });

    // 侧边栏菜单项
    document.querySelectorAll('.menu-item').forEach(item => {
      item.addEventListener('click', (e) => this.handleMenuClick(e, item));
    });

    // 操作菜单项
    document.querySelectorAll('.action-item').forEach(item => {
      item.addEventListener('click', (e) => this.handleActionClick(e, item));
    });

    // 搜索输入
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
      searchInput.addEventListener('input', (e) => this.handleSearch(e.target.value));
    }

    // 面包屑导航
    document.querySelectorAll('.breadcrumb-item').forEach(item => {
      item.addEventListener('click', (e) => this.handleBreadcrumbClick(e, item));
    });

    // 设备方向变化
    window.addEventListener('orientationchange', () => {
      setTimeout(() => this.handleOrientationChange(), 100);
    });

    // 窗口大小变化
    window.addEventListener('resize', () => this.handleResize());
  }

  initGestures() {
    // 侧边栏滑动手势
    let startX = 0;
    let currentX = 0;
    let isDragging = false;

    document.addEventListener('touchstart', (e) => {
      startX = e.touches[0].clientX;
      if (startX < 20 || this.sidebarOpen) {
        isDragging = true;
      }
    }, { passive: true });

    document.addEventListener('touchmove', (e) => {
      if (!isDragging) return;
      
      currentX = e.touches[0].clientX;
      const deltaX = currentX - startX;
      
      if (this.sidebarOpen) {
        // 关闭手势
        if (deltaX < -50) {
          const sidebarContent = this.sidebar.querySelector('.sidebar-content');
          sidebarContent.style.transform = `translateX(${Math.max(deltaX, -280)}px)`;
        }
      } else {
        // 打开手势
        if (deltaX > 50 && startX < 20) {
          this.sidebar.classList.add('open');
          const sidebarContent = this.sidebar.querySelector('.sidebar-content');
          sidebarContent.style.transform = `translateX(${Math.min(deltaX - 280, 0)}px)`;
        }
      }
    }, { passive: true });

    document.addEventListener('touchend', (e) => {
      if (!isDragging) return;
      
      const deltaX = currentX - startX;
      const sidebarContent = this.sidebar.querySelector('.sidebar-content');
      
      if (this.sidebarOpen && deltaX < -100) {
        this.closeSidebar();
      } else if (!this.sidebarOpen && deltaX > 100 && startX < 20) {
        this.openSidebar();
      } else {
        // 重置位置
        sidebarContent.style.transform = '';
        if (!this.sidebarOpen) {
          this.sidebar.classList.remove('open');
        }
      }
      
      isDragging = false;
    }, { passive: true });

    // 下拉刷新手势
    this.initPullToRefresh();
  }

  initPullToRefresh() {
    let startY = 0;
    let pullDistance = 0;
    const fileList = document.querySelector('.file-list-mobile');
    
    fileList.addEventListener('touchstart', (e) => {
      if (fileList.scrollTop === 0) {
        startY = e.touches[0].clientY;
      }
    }, { passive: true });

    fileList.addEventListener('touchmove', (e) => {
      if (fileList.scrollTop === 0 && startY > 0) {
        pullDistance = e.touches[0].clientY - startY;
        if (pullDistance > 0) {
          e.preventDefault();
          const translateY = Math.min(pullDistance * 0.5, 50);
          fileList.style.transform = `translateY(${translateY}px)`;
          
          // 添加下拉刷新指示器
          if (pullDistance > 80) {
            this.showPullRefreshIndicator();
          }
        }
      }
    });

    fileList.addEventListener('touchend', () => {
      if (pullDistance > 80) {
        this.refreshFileList();
      }
      fileList.style.transform = '';
      this.hidePullRefreshIndicator();
      startY = 0;
      pullDistance = 0;
    }, { passive: true });
  }

  applyDeviceStyles() {
    if (this.isIOS) {
      document.body.classList.add('ios');
    }
    
    if (this.hasNotch) {
      document.body.classList.add('has-notch');
    }
  }

  initLiquidEffects() {
    // 为移动端元素添加液态效果
    document.querySelectorAll('.mobile-file-item').forEach(item => {
      item.classList.add('liquid-card');
    });

    document.querySelectorAll('.quick-btn').forEach(btn => {
      btn.classList.add('liquid-button');
    });

    document.querySelectorAll('.menu-item').forEach(item => {
      item.classList.add('liquid-nav-item');
    });

    // 添加触摸反馈
    this.addTouchFeedback();
  }

  addTouchFeedback() {
    const touchElements = document.querySelectorAll(
      '.mobile-file-item, .quick-btn, .nav-tab, .action-item, .menu-item'
    );

    touchElements.forEach(element => {
      element.addEventListener('touchstart', () => {
        element.style.transform = 'scale(0.98)';
        element.style.opacity = '0.8';
      }, { passive: true });

      element.addEventListener('touchend', () => {
        setTimeout(() => {
          element.style.transform = '';
          element.style.opacity = '';
        }, 100);
      }, { passive: true });
    });
  }

  toggleSidebar() {
    if (this.sidebarOpen) {
      this.closeSidebar();
    } else {
      this.openSidebar();
    }
  }

  openSidebar() {
    this.sidebarOpen = true;
    this.sidebar.classList.add('open');
    document.body.style.overflow = 'hidden';
  }

  closeSidebar() {
    this.sidebarOpen = false;
    this.sidebar.classList.remove('open');
    document.body.style.overflow = '';
  }

  openSearch() {
    this.searchOpen = true;
    this.searchOverlay.classList.add('open');
    // 自动聚焦搜索框
    setTimeout(() => {
      document.getElementById('searchInput').focus();
    }, 300);
  }

  closeSearch() {
    this.searchOpen = false;
    this.searchOverlay.classList.remove('open');
  }

  showActionSheet() {
    this.actionSheetOpen = true;
    this.actionSheet.classList.add('open');
    document.body.style.overflow = 'hidden';
  }

  closeActionSheet() {
    this.actionSheetOpen = false;
    this.actionSheet.classList.remove('open');
    document.body.style.overflow = '';
  }

  handleFileClick(e, item) {
    // 添加点击动画
    item.style.transform = 'scale(0.98)';
    setTimeout(() => {
      item.style.transform = '';
    }, 150);

    const fileType = item.dataset.type;
    const fileName = item.querySelector('.file-name').textContent;
    
    console.log(`Opening ${fileType}: ${fileName}`);
    
    // 文件打开逻辑
    if (fileType === 'folder') {
      this.openFolder(fileName);
    } else {
      this.openFile(fileName, fileType);
    }
  }

  handleQuickAction(e, btn) {
    const action = btn.querySelector('span').textContent;
    
    console.log(`Quick action: ${action}`);
    
    switch (action) {
      case '新建':
        this.showCreateOptions();
        break;
      case '上传':
        this.showUploadOptions();
        break;
      case '拍照':
        this.openCamera();
        break;
      case '文件夹':
        this.createFolder();
        break;
      case '分享':
        this.showShareOptions();
        break;
    }
  }

  handleNavClick(e, tab) {
    e.preventDefault();
    
    // 更新导航状态
    document.querySelectorAll('.nav-tab').forEach(t => {
      t.classList.remove('active');
    });
    tab.classList.add('active');

    const navText = tab.querySelector('span').textContent;
    console.log(`Navigation: ${navText}`);
  }

  handleMenuClick(e, item) {
    e.preventDefault();
    
    const menuText = item.querySelector('span').textContent;
    console.log(`Menu: ${menuText}`);
    
    this.closeSidebar();
  }

  handleActionClick(e, item) {
    const action = item.querySelector('span').textContent;
    console.log(`Action: ${action}`);
    
    this.closeActionSheet();
    
    // 执行相应操作
    switch (action) {
      case '打开':
        this.openSelectedFiles();
        break;
      case '下载':
        this.downloadSelectedFiles();
        break;
      case '分享':
        this.shareSelectedFiles();
        break;
      case '收藏':
        this.favoriteSelectedFiles();
        break;
      case '重命名':
        this.renameSelectedFile();
        break;
      case '删除':
        this.deleteSelectedFiles();
        break;
    }
  }

  handleSearch(query) {
    console.log(`Searching for: ${query}`);
    // 实现搜索逻辑
  }

  handleBreadcrumbClick(e, item) {
    e.preventDefault();
    const path = item.textContent;
    console.log(`Navigate to: ${path}`);
  }

  handleOrientationChange() {
    // 处理设备方向变化
    this.closeAllOverlays();
  }

  handleResize() {
    // 处理窗口大小变化
    this.closeAllOverlays();
  }

  closeAllOverlays() {
    this.closeSidebar();
    this.closeSearch();
    this.closeActionSheet();
  }

  updateTime() {
    const timeElement = document.querySelector('.time');
    if (timeElement) {
      const now = new Date();
      const hours = now.getHours().toString().padStart(2, '0');
      const minutes = now.getMinutes().toString().padStart(2, '0');
      timeElement.textContent = `${hours}:${minutes}`;
    }
    
    // 每分钟更新一次
    setTimeout(() => this.updateTime(), 60000);
  }

  showPullRefreshIndicator() {
    console.log('Show pull refresh indicator');
  }

  hidePullRefreshIndicator() {
    console.log('Hide pull refresh indicator');
  }

  refreshFileList() {
    console.log('Refreshing file list...');
    // 实现下拉刷新逻辑
  }

  // 文件操作方法
  openFolder(folderName) {
    console.log(`Opening folder: ${folderName}`);
    // 更新面包屑导航
    this.updateBreadcrumb(folderName);
  }

  openFile(fileName, fileType) {
    console.log(`Opening file: ${fileName} (${fileType})`);
  }

  updateBreadcrumb(folderName) {
    const breadcrumb = document.querySelector('.breadcrumb-content');
    const separator = document.createElement('i');
    separator.className = 'fas fa-chevron-right breadcrumb-separator';
    const newItem = document.createElement('span');
    newItem.className = 'breadcrumb-current';
    newItem.textContent = folderName;
    
    // 将当前项改为链接
    const currentItem = breadcrumb.querySelector('.breadcrumb-current');
    if (currentItem) {
      const link = document.createElement('a');
      link.href = '#';
      link.className = 'breadcrumb-item';
      link.textContent = currentItem.textContent;
      currentItem.replaceWith(link);
    }
    
    breadcrumb.appendChild(separator);
    breadcrumb.appendChild(newItem);
  }

  showCreateOptions() {
    console.log('Show create options');
  }

  showUploadOptions() {
    console.log('Show upload options');
  }

  openCamera() {
    console.log('Open camera');
  }

  createFolder() {
    console.log('Create folder');
  }

  showShareOptions() {
    console.log('Show share options');
  }

  openSelectedFiles() {
    console.log('Open selected files');
  }

  downloadSelectedFiles() {
    console.log('Download selected files');
  }

  shareSelectedFiles() {
    console.log('Share selected files');
  }

  favoriteSelectedFiles() {
    console.log('Favorite selected files');
  }

  renameSelectedFile() {
    console.log('Rename selected file');
  }

  deleteSelectedFiles() {
    console.log('Delete selected files');
  }
}

// 初始化移动端应用
document.addEventListener('DOMContentLoaded', () => {
  new MobileCloudDriveApp();
});

// 防止双击缩放
document.addEventListener('touchstart', function(event) {
  if (event.touches.length > 1) {
    event.preventDefault();
  }
});

let lastTouchEnd = 0;
document.addEventListener('touchend', function(event) {
  const now = (new Date()).getTime();
  if (now - lastTouchEnd <= 300) {
    event.preventDefault();
  }
  lastTouchEnd = now;
}, false);
