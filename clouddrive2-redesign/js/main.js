// CloudDrive2 现代化UI交互脚本

class CloudDriveUI {
  constructor() {
    this.init();
    this.bindEvents();
    this.initLiquidEffects();
  }

  init() {
    // 初始化视图状态
    this.currentView = 'grid';
    this.selectedFiles = new Set();
    this.contextMenu = document.getElementById('contextMenu');
    
    // 检测设备类型
    this.isMobile = window.innerWidth <= 768;
    this.isTouch = 'ontouchstart' in window;
    
    // 初始化云盘图标
    this.initCloudIcons();
    
    // 应用液态玻璃效果
    this.applyLiquidGlassEffects();
  }

  bindEvents() {
    // 视图切换
    document.querySelectorAll('.view-btn').forEach(btn => {
      btn.addEventListener('click', (e) => this.switchView(e.target.dataset.view));
    });

    // 文件项点击
    document.querySelectorAll('.file-item').forEach(item => {
      item.addEventListener('click', (e) => this.handleFileClick(e, item));
      item.addEventListener('contextmenu', (e) => this.showContextMenu(e, item));
    });

    // 搜索功能
    const searchInput = document.querySelector('.search-input');
    if (searchInput) {
      searchInput.addEventListener('input', (e) => this.handleSearch(e.target.value));
      searchInput.addEventListener('focus', (e) => this.handleSearchFocus(e));
      searchInput.addEventListener('blur', (e) => this.handleSearchBlur(e));
    }

    // 导航项点击
    document.querySelectorAll('.nav-item').forEach(item => {
      item.addEventListener('click', (e) => this.handleNavClick(e, item));
    });

    // 移动端侧边栏
    if (this.isMobile) {
      this.initMobileSidebar();
    }

    // 右键菜单
    document.addEventListener('click', () => this.hideContextMenu());
    
    // 窗口大小变化
    window.addEventListener('resize', () => this.handleResize());

    // 键盘快捷键
    document.addEventListener('keydown', (e) => this.handleKeyboard(e));
  }

  initCloudIcons() {
    const cloudIcons = document.querySelectorAll('.cloud-icon');
    const iconMap = {
      'aliyun': '阿',
      'baidu': '百',
      'onedrive': 'O',
      'googledrive': 'G'
    };

    cloudIcons.forEach(icon => {
      const type = Array.from(icon.classList).find(cls => cls !== 'cloud-icon');
      if (type && iconMap[type]) {
        icon.textContent = iconMap[type];
      }
    });
  }

  applyLiquidGlassEffects() {
    // 为侧边栏添加液态玻璃效果
    const sidebar = document.querySelector('.sidebar-glass');
    if (sidebar) {
      sidebar.classList.add('liquid-glass');
    }

    // 为头部添加液态玻璃效果
    const header = document.querySelector('.header-glass');
    if (header) {
      header.classList.add('liquid-glass');
    }

    // 为文件项添加液态卡片效果
    document.querySelectorAll('.file-item').forEach(item => {
      item.classList.add('liquid-card');
    });

    // 为按钮添加液态按钮效果
    document.querySelectorAll('.action-btn-primary, .action-btn-secondary').forEach(btn => {
      btn.classList.add('liquid-button');
    });

    // 为导航项添加液态导航效果
    document.querySelectorAll('.nav-item').forEach(item => {
      item.classList.add('liquid-nav-item');
    });

    // 为搜索框添加液态输入框效果
    const searchInput = document.querySelector('.search-input');
    if (searchInput) {
      searchInput.classList.add('liquid-input');
    }
  }

  switchView(view) {
    this.currentView = view;
    
    // 更新按钮状态
    document.querySelectorAll('.view-btn').forEach(btn => {
      btn.classList.toggle('active', btn.dataset.view === view);
    });

    // 切换视图
    const fileGrid = document.getElementById('fileGrid');
    if (view === 'list') {
      fileGrid.classList.add('file-list');
      fileGrid.classList.remove('file-grid');
    } else {
      fileGrid.classList.add('file-grid');
      fileGrid.classList.remove('file-list');
    }

    // 添加切换动画
    fileGrid.style.opacity = '0';
    setTimeout(() => {
      fileGrid.style.opacity = '1';
    }, 150);
  }

  handleFileClick(e, item) {
    e.preventDefault();
    
    // 处理多选
    if (e.ctrlKey || e.metaKey) {
      this.toggleFileSelection(item);
    } else if (e.shiftKey && this.selectedFiles.size > 0) {
      this.selectFileRange(item);
    } else {
      this.selectSingleFile(item);
    }

    // 双击打开文件
    if (e.detail === 2) {
      this.openFile(item);
    }
  }

  toggleFileSelection(item) {
    if (this.selectedFiles.has(item)) {
      this.selectedFiles.delete(item);
      item.classList.remove('selected');
    } else {
      this.selectedFiles.add(item);
      item.classList.add('selected');
    }
  }

  selectSingleFile(item) {
    // 清除之前的选择
    this.selectedFiles.forEach(file => file.classList.remove('selected'));
    this.selectedFiles.clear();
    
    // 选择当前文件
    this.selectedFiles.add(item);
    item.classList.add('selected');
  }

  openFile(item) {
    const fileType = item.dataset.type;
    const fileName = item.querySelector('.file-name').textContent;
    
    // 添加打开动画
    item.style.transform = 'scale(0.95)';
    setTimeout(() => {
      item.style.transform = '';
    }, 150);

    console.log(`Opening ${fileType}: ${fileName}`);
    // 这里可以添加实际的文件打开逻辑
  }

  showContextMenu(e, item) {
    e.preventDefault();
    
    if (!this.selectedFiles.has(item)) {
      this.selectSingleFile(item);
    }

    const menu = this.contextMenu;
    menu.style.display = 'block';
    menu.style.left = e.pageX + 'px';
    menu.style.top = e.pageY + 'px';

    // 确保菜单不会超出屏幕
    const rect = menu.getBoundingClientRect();
    if (rect.right > window.innerWidth) {
      menu.style.left = (e.pageX - rect.width) + 'px';
    }
    if (rect.bottom > window.innerHeight) {
      menu.style.top = (e.pageY - rect.height) + 'px';
    }
  }

  hideContextMenu() {
    this.contextMenu.style.display = 'none';
  }

  handleSearch(query) {
    const fileItems = document.querySelectorAll('.file-item');
    
    fileItems.forEach(item => {
      const fileName = item.querySelector('.file-name').textContent.toLowerCase();
      const matches = fileName.includes(query.toLowerCase());
      
      item.style.display = matches ? 'block' : 'none';
      
      if (matches && query) {
        // 高亮搜索结果
        item.classList.add('search-highlight');
      } else {
        item.classList.remove('search-highlight');
      }
    });
  }

  handleSearchFocus(e) {
    e.target.parentElement.classList.add('search-focused');
  }

  handleSearchBlur(e) {
    e.target.parentElement.classList.remove('search-focused');
  }

  handleNavClick(e, item) {
    e.preventDefault();
    
    // 更新导航状态
    document.querySelectorAll('.nav-item').forEach(nav => {
      nav.classList.remove('active');
    });
    item.classList.add('active');

    // 添加点击动画
    item.style.transform = 'scale(0.98)';
    setTimeout(() => {
      item.style.transform = '';
    }, 100);
  }

  initMobileSidebar() {
    // 创建汉堡菜单按钮
    const hamburger = document.createElement('button');
    hamburger.className = 'hamburger-btn';
    hamburger.innerHTML = '<i class="fas fa-bars"></i>';
    
    const header = document.querySelector('.header-glass .flex');
    header.insertBefore(hamburger, header.firstChild);

    // 绑定事件
    hamburger.addEventListener('click', () => {
      const sidebar = document.querySelector('.sidebar-glass');
      sidebar.classList.toggle('open');
    });

    // 点击遮罩关闭侧边栏
    const overlay = document.createElement('div');
    overlay.className = 'sidebar-overlay';
    document.body.appendChild(overlay);

    overlay.addEventListener('click', () => {
      document.querySelector('.sidebar-glass').classList.remove('open');
    });
  }

  handleResize() {
    const wasMobile = this.isMobile;
    this.isMobile = window.innerWidth <= 768;
    
    if (wasMobile !== this.isMobile) {
      // 设备类型改变，重新初始化
      location.reload();
    }
  }

  handleKeyboard(e) {
    // Ctrl/Cmd + A: 全选
    if ((e.ctrlKey || e.metaKey) && e.key === 'a') {
      e.preventDefault();
      this.selectAllFiles();
    }
    
    // Delete: 删除选中文件
    if (e.key === 'Delete' && this.selectedFiles.size > 0) {
      this.deleteSelectedFiles();
    }
    
    // Escape: 取消选择
    if (e.key === 'Escape') {
      this.clearSelection();
      this.hideContextMenu();
    }
  }

  selectAllFiles() {
    const fileItems = document.querySelectorAll('.file-item:not([style*="display: none"])');
    this.selectedFiles.clear();
    
    fileItems.forEach(item => {
      this.selectedFiles.add(item);
      item.classList.add('selected');
    });
  }

  clearSelection() {
    this.selectedFiles.forEach(file => file.classList.remove('selected'));
    this.selectedFiles.clear();
  }

  deleteSelectedFiles() {
    if (confirm(`确定要删除选中的 ${this.selectedFiles.size} 个文件吗？`)) {
      this.selectedFiles.forEach(file => {
        file.style.animation = 'fadeOut 0.3s ease';
        setTimeout(() => file.remove(), 300);
      });
      this.selectedFiles.clear();
    }
  }

  // 初始化液态效果
  initLiquidEffects() {
    // 为悬浮元素添加浮动效果
    document.querySelectorAll('.file-icon').forEach((icon, index) => {
      setTimeout(() => {
        icon.classList.add('liquid-float');
      }, index * 100);
    });

    // 为状态指示器添加脉冲效果
    document.querySelectorAll('.status-indicator.online').forEach(indicator => {
      indicator.classList.add('liquid-pulse');
    });

    // 为按钮添加波纹效果
    document.querySelectorAll('button').forEach(btn => {
      btn.classList.add('liquid-ripple');
    });
  }
}

// 添加CSS动画
const style = document.createElement('style');
style.textContent = `
  @keyframes fadeOut {
    from { opacity: 1; transform: scale(1); }
    to { opacity: 0; transform: scale(0.8); }
  }
  
  .selected {
    background: rgba(0, 122, 255, 0.1) !important;
    border-color: rgba(0, 122, 255, 0.3) !important;
    transform: scale(1.02);
  }
  
  .search-highlight {
    animation: searchPulse 1s ease-in-out;
  }
  
  @keyframes searchPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
  }
  
  .search-focused {
    transform: scale(1.02);
  }
  
  .hamburger-btn {
    display: none;
    padding: 8px;
    background: rgba(255, 255, 255, 0.8);
    border: none;
    border-radius: 8px;
    margin-right: 16px;
  }
  
  .sidebar-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    z-index: 999;
  }
  
  @media (max-width: 768px) {
    .hamburger-btn {
      display: block;
    }
    
    .sidebar-glass.open + .sidebar-overlay {
      display: block;
    }
  }
`;
document.head.appendChild(style);

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
  new CloudDriveUI();
});

// 性能优化：防抖函数
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}
