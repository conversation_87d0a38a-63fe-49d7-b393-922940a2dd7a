// 液态玻璃效果库 - iOS 16风格

class LiquidGlass {
  constructor(options = {}) {
    this.options = {
      intensity: options.intensity || 1,
      speed: options.speed || 1,
      enableParticles: options.enableParticles !== false,
      enableWaves: options.enableWaves !== false,
      enableGlow: options.enableGlow !== false,
      ...options
    };
    
    this.init();
  }

  init() {
    this.createParticleSystem();
    this.createWaveSystem();
    this.bindEvents();
    this.startAnimation();
  }

  createParticleSystem() {
    if (!this.options.enableParticles) return;

    this.particleContainer = document.createElement('div');
    this.particleContainer.className = 'liquid-particles';
    this.particleContainer.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      z-index: -1;
      overflow: hidden;
    `;
    
    document.body.appendChild(this.particleContainer);
    
    // 创建粒子
    this.particles = [];
    for (let i = 0; i < 20; i++) {
      this.createParticle();
    }
  }

  createParticle() {
    const particle = document.createElement('div');
    particle.className = 'liquid-particle';
    
    const size = Math.random() * 4 + 2;
    const x = Math.random() * window.innerWidth;
    const y = Math.random() * window.innerHeight;
    const opacity = Math.random() * 0.3 + 0.1;
    
    particle.style.cssText = `
      position: absolute;
      width: ${size}px;
      height: ${size}px;
      background: radial-gradient(circle, rgba(255, 255, 255, ${opacity}), transparent);
      border-radius: 50%;
      left: ${x}px;
      top: ${y}px;
      animation: liquidFloat ${3 + Math.random() * 4}s ease-in-out infinite;
      animation-delay: ${Math.random() * 2}s;
    `;
    
    this.particleContainer.appendChild(particle);
    this.particles.push({
      element: particle,
      x: x,
      y: y,
      vx: (Math.random() - 0.5) * 0.5,
      vy: (Math.random() - 0.5) * 0.5,
      size: size
    });
  }

  createWaveSystem() {
    if (!this.options.enableWaves) return;

    this.waveContainer = document.createElement('div');
    this.waveContainer.className = 'liquid-waves';
    this.waveContainer.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      z-index: -2;
      overflow: hidden;
    `;
    
    document.body.appendChild(this.waveContainer);
    
    // 创建SVG波浪
    this.createSVGWaves();
  }

  createSVGWaves() {
    const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
    svg.setAttribute('width', '100%');
    svg.setAttribute('height', '100%');
    svg.style.cssText = `
      position: absolute;
      top: 0;
      left: 0;
    `;

    // 创建渐变定义
    const defs = document.createElementNS('http://www.w3.org/2000/svg', 'defs');
    const gradient = document.createElementNS('http://www.w3.org/2000/svg', 'linearGradient');
    gradient.setAttribute('id', 'waveGradient');
    gradient.setAttribute('x1', '0%');
    gradient.setAttribute('y1', '0%');
    gradient.setAttribute('x2', '100%');
    gradient.setAttribute('y2', '100%');

    const stop1 = document.createElementNS('http://www.w3.org/2000/svg', 'stop');
    stop1.setAttribute('offset', '0%');
    stop1.setAttribute('stop-color', 'rgba(0, 122, 255, 0.1)');

    const stop2 = document.createElementNS('http://www.w3.org/2000/svg', 'stop');
    stop2.setAttribute('offset', '50%');
    stop2.setAttribute('stop-color', 'rgba(88, 86, 214, 0.05)');

    const stop3 = document.createElementNS('http://www.w3.org/2000/svg', 'stop');
    stop3.setAttribute('offset', '100%');
    stop3.setAttribute('stop-color', 'rgba(255, 45, 85, 0.1)');

    gradient.appendChild(stop1);
    gradient.appendChild(stop2);
    gradient.appendChild(stop3);
    defs.appendChild(gradient);
    svg.appendChild(defs);

    // 创建波浪路径
    for (let i = 0; i < 3; i++) {
      const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
      path.setAttribute('fill', 'url(#waveGradient)');
      path.setAttribute('opacity', 0.3 - i * 0.1);
      path.style.animation = `liquidWave ${8 + i * 2}s ease-in-out infinite`;
      path.style.animationDelay = `${i * 0.5}s`;
      
      svg.appendChild(path);
      this.updateWavePath(path, i);
    }

    this.waveContainer.appendChild(svg);
  }

  updateWavePath(path, index) {
    const width = window.innerWidth;
    const height = window.innerHeight;
    const amplitude = 50 + index * 20;
    const frequency = 0.01 + index * 0.005;
    const offset = index * Math.PI / 3;

    let pathData = `M 0 ${height / 2}`;
    
    for (let x = 0; x <= width; x += 10) {
      const y = height / 2 + Math.sin(x * frequency + offset) * amplitude;
      pathData += ` L ${x} ${y}`;
    }
    
    pathData += ` L ${width} ${height} L 0 ${height} Z`;
    path.setAttribute('d', pathData);
  }

  bindEvents() {
    // 鼠标移动效果
    document.addEventListener('mousemove', (e) => {
      this.handleMouseMove(e);
    });

    // 触摸事件
    document.addEventListener('touchmove', (e) => {
      if (e.touches.length > 0) {
        this.handleMouseMove(e.touches[0]);
      }
    });

    // 窗口大小变化
    window.addEventListener('resize', () => {
      this.handleResize();
    });

    // 元素悬停效果
    this.bindHoverEffects();
  }

  handleMouseMove(e) {
    const x = e.clientX / window.innerWidth;
    const y = e.clientY / window.innerHeight;
    
    // 更新粒子位置
    if (this.particles) {
      this.particles.forEach(particle => {
        const dx = e.clientX - particle.x;
        const dy = e.clientY - particle.y;
        const distance = Math.sqrt(dx * dx + dy * dy);
        
        if (distance < 100) {
          const force = (100 - distance) / 100;
          particle.vx += (dx / distance) * force * 0.01;
          particle.vy += (dy / distance) * force * 0.01;
        }
      });
    }

    // 创建鼠标跟随效果
    this.createMouseTrail(e.clientX, e.clientY);
  }

  createMouseTrail(x, y) {
    const trail = document.createElement('div');
    trail.className = 'mouse-trail';
    trail.style.cssText = `
      position: fixed;
      left: ${x - 5}px;
      top: ${y - 5}px;
      width: 10px;
      height: 10px;
      background: radial-gradient(circle, rgba(0, 122, 255, 0.3), transparent);
      border-radius: 50%;
      pointer-events: none;
      z-index: 9999;
      animation: trailFade 1s ease-out forwards;
    `;
    
    document.body.appendChild(trail);
    
    setTimeout(() => {
      if (trail.parentNode) {
        trail.parentNode.removeChild(trail);
      }
    }, 1000);
  }

  bindHoverEffects() {
    // 为所有液态玻璃元素添加悬停效果
    document.querySelectorAll('.liquid-glass, .liquid-card, .liquid-button').forEach(element => {
      element.addEventListener('mouseenter', (e) => {
        this.createHoverEffect(e.target);
      });
      
      element.addEventListener('mouseleave', (e) => {
        this.removeHoverEffect(e.target);
      });
    });
  }

  createHoverEffect(element) {
    // 添加发光效果
    if (this.options.enableGlow) {
      element.style.boxShadow = `
        0 0 20px rgba(0, 122, 255, 0.3),
        0 8px 32px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2)
      `;
    }

    // 添加波纹效果
    const ripple = document.createElement('div');
    ripple.className = 'hover-ripple';
    ripple.style.cssText = `
      position: absolute;
      top: 50%;
      left: 50%;
      width: 0;
      height: 0;
      background: radial-gradient(circle, rgba(255, 255, 255, 0.3), transparent);
      border-radius: 50%;
      transform: translate(-50%, -50%);
      animation: rippleExpand 0.6s ease-out;
      pointer-events: none;
    `;
    
    element.style.position = 'relative';
    element.appendChild(ripple);
    
    setTimeout(() => {
      if (ripple.parentNode) {
        ripple.parentNode.removeChild(ripple);
      }
    }, 600);
  }

  removeHoverEffect(element) {
    // 移除发光效果
    element.style.boxShadow = '';
  }

  startAnimation() {
    const animate = () => {
      this.updateParticles();
      this.updateWaves();
      requestAnimationFrame(animate);
    };
    
    animate();
  }

  updateParticles() {
    if (!this.particles) return;

    this.particles.forEach(particle => {
      // 更新位置
      particle.x += particle.vx;
      particle.y += particle.vy;
      
      // 边界检测
      if (particle.x < 0 || particle.x > window.innerWidth) {
        particle.vx *= -0.8;
        particle.x = Math.max(0, Math.min(window.innerWidth, particle.x));
      }
      
      if (particle.y < 0 || particle.y > window.innerHeight) {
        particle.vy *= -0.8;
        particle.y = Math.max(0, Math.min(window.innerHeight, particle.y));
      }
      
      // 阻尼
      particle.vx *= 0.99;
      particle.vy *= 0.99;
      
      // 应用位置
      particle.element.style.left = particle.x + 'px';
      particle.element.style.top = particle.y + 'px';
    });
  }

  updateWaves() {
    // 波浪动画通过CSS处理，这里可以添加额外的动态效果
  }

  handleResize() {
    // 重新计算粒子位置
    if (this.particles) {
      this.particles.forEach(particle => {
        if (particle.x > window.innerWidth) {
          particle.x = window.innerWidth - 10;
        }
        if (particle.y > window.innerHeight) {
          particle.y = window.innerHeight - 10;
        }
      });
    }
  }

  destroy() {
    // 清理资源
    if (this.particleContainer) {
      this.particleContainer.remove();
    }
    if (this.waveContainer) {
      this.waveContainer.remove();
    }
  }
}

// 添加必要的CSS动画
const liquidGlassStyles = document.createElement('style');
liquidGlassStyles.textContent = `
  @keyframes liquidFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-10px) rotate(120deg); }
    66% { transform: translateY(5px) rotate(240deg); }
  }
  
  @keyframes liquidWave {
    0%, 100% { transform: translateX(0px); }
    50% { transform: translateX(-20px); }
  }
  
  @keyframes trailFade {
    0% { opacity: 1; transform: scale(1); }
    100% { opacity: 0; transform: scale(0); }
  }
  
  @keyframes rippleExpand {
    0% { width: 0; height: 0; opacity: 1; }
    100% { width: 200px; height: 200px; opacity: 0; }
  }
`;
document.head.appendChild(liquidGlassStyles);

// 自动初始化
document.addEventListener('DOMContentLoaded', () => {
  window.liquidGlass = new LiquidGlass({
    intensity: 1,
    speed: 1,
    enableParticles: true,
    enableWaves: true,
    enableGlow: true
  });
});

// 导出类
window.LiquidGlass = LiquidGlass;
