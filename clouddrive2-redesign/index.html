<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CloudDrive Neo</title>
    <link rel="stylesheet" href="style.css">
    <!-- 引入图标库，例如 Feather Icons -->
    <script src="https://unpkg.com/feather-icons"></script>
    <!-- Favicon placeholder (optional) -->
    <!-- <link rel="icon" href="favicon.ico" type="image/x-icon"> -->
</head>
<body>
    <!-- Mobile Hamburger Menu Placeholder (to be styled and controlled by J<PERSON>) -->
    <!-- 
    <button class="hamburger-menu" aria-label="Toggle navigation" aria-expanded="false">
        <i data-feather="menu"></i>
    </button>
    -->

    <div class="app-container">
        <!-- 左侧导航栏 -->
        <aside class="sidebar left-sidebar">
            <div class="sidebar-header">
                <!-- Replace with your actual logo -->
                <svg class="logo" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg" fill="currentColor">
                    <circle cx="50" cy="50" r="40" stroke="currentColor" stroke-width="8" fill="none" />
                    <path d="M30 60 Q 50 30 70 60 Q 50 90 30 60Z" />
                </svg>
                <span>CloudDrive Neo</span>
            </div>
            <nav class="main-nav">
                <ul>
                    <li class="nav-item active">
                        <a href="#">
                            <i data-feather="hard-drive"></i>
                            <span>115 云盘</span>
                            <i data-feather="chevron-down" class="sub-indicator"></i>
                        </a>
                        <ul class="sub-nav show">
                            <li><a href="#"><i data-feather="folder"></i> Anime</a></li>
                            <li><a href="#"><i data-feather="folder"></i> downloads</a></li>
                            <li><a href="#"><i data-feather="folder"></i> HDHome</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a href="#">
                            <i data-feather="cloud"></i>
                            <span>阿里云盘Open</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#">
                            <i data-feather="share-2"></i>
                            <span>WebDAV</span>
                            <i data-feather="chevron-down" class="sub-indicator"></i>
                        </a>
                        <ul class="sub-nav">
                             <li><a href="#"><i data-feather="server"></i> zhiwan112233@...</a></li>
                             <li><a href="#"><i data-feather="server"></i> 天翼云盘@189.cn</a></li>
                        </ul>
                    </li>
                     <li class="nav-item">
                        <a href="#">
                            <i data-feather="smartphone"></i> <!-- Example for a different icon -->
                            <span>移动设备备份</span>
                        </a>
                    </li>
                     <li class="nav-item">
                        <a href="#">
                            <i data-feather="trash-2"></i>
                            <span>回收站</span>
                        </a>
                    </li>
                </ul>
            </nav>
            <div class="sidebar-footer">
                <button class="settings-btn action-button" title="设置">
                    <i data-feather="settings"></i>
                    <span>设置</span>
                </button>
            </div>
        </aside>

        <!-- 主内容区 -->
        <main class="main-content">
            <header class="main-header">
                <div class="breadcrumb">
                    <i data-feather="home" class="breadcrumb-icon"></i>
                    <span>/</span>
                    <a href="#" class="breadcrumb-link">115 云盘</a>
                    <span>/</span>
                    <span class="current-folder">Anime</span>
                </div>
                <div class="toolbar">
                    <input type="search" placeholder="搜索文件..." class="search-bar" aria-label="搜索文件">
                    <button class="tool-btn" title="新建文件夹" aria-label="新建文件夹"><i data-feather="folder-plus"></i></button>
                    <button class="tool-btn" title="上传文件" aria-label="上传文件"><i data-feather="upload-cloud"></i></button>
                    <button class="tool-btn view-toggle" data-view="list" title="列表视图" aria-label="列表视图"><i data-feather="list"></i></button>
                    <button class="tool-btn view-toggle active" data-view="grid" title="网格视图" aria-label="网格视图"><i data-feather="grid"></i></button>
                    <button class="tool-btn" title="更多操作" aria-label="更多操作"><i data-feather="more-horizontal"></i></button>
                </div>
            </header>

            <div class="file-browser" id="fileBrowser">
                <!-- 文件和文件夹将由 JavaScript 动态生成或从模板填充 -->
                <!-- 示例 网格视图 项目 -->
                <div class="file-item grid-view" tabindex="0" data-type="folder" data-name="Movie Collection" data-modified="2023-10-26">
                    <div class="file-icon"><i data-feather="folder"></i></div>
                    <div class="file-name-container">
                        <span class="file-name">Movie Collection</span>
                    </div>
                    <div class="file-info">文件夹</div>
                </div>
                <div class="file-item grid-view" tabindex="0" data-type="video" data-name="Epic_Adventure.mkv" data-size="1.7 GB" data-modified="2023-08-15">
                    <div class="file-icon"><i data-feather="film"></i></div>
                     <div class="file-name-container">
                        <span class="file-name">Epic_Adventure.mkv</span>
                    </div>
                    <div class="file-info">1.7 GB</div>
                </div>
                <div class="file-item grid-view" tabindex="0" data-type="image" data-name="Mountain_View.jpg" data-size="2.3 MB" data-modified="2023-09-01">
                    <div class="file-icon"><i data-feather="image"></i></div>
                     <div class="file-name-container">
                        <span class="file-name">Mountain_View.jpg</span>
                    </div>
                    <div class="file-info">2.3 MB</div>
                </div>
                <div class="file-item grid-view" tabindex="0" data-type="document" data-name="Project_Proposal.pdf" data-size="512 KB" data-modified="2023-10-10">
                    <div class="file-icon"><i data-feather="file-text"></i></div>
                     <div class="file-name-container">
                        <span class="file-name">Project_Proposal.pdf</span>
                    </div>
                    <div class="file-info">512 KB</div>
                </div>
                <div class="file-item grid-view" tabindex="0" data-type="archive" data-name="Backup_Files.zip" data-size="128 MB" data-modified="2023-07-20">
                    <div class="file-icon"><i data-feather="archive"></i></div>
                     <div class="file-name-container">
                        <span class="file-name">Backup_Files.zip</span>
                    </div>
                    <div class="file-info">128 MB</div>
                </div>

                <!-- 示例 列表视图 项目 (JS会切换类名来改变显示) -->
                <!--
                <div class="file-item list-view" tabindex="0" data-type="folder" data-name="Documents" data-modified="2023-10-25">
                    <div class="file-icon-list"><i data-feather="folder"></i></div>
                    <div class="file-name-list">Documents</div>
                    <div class="file-date-list">2023-10-25</div>
                    <div class="file-size-list">--</div>
                    <div class="file-actions-list"><i data-feather="more-vertical"></i></div>
                </div>
                -->
            </div>
            
            <button id="showLiquidGlass" class="action-button" style="position: fixed; bottom: 20px; right: 20px; z-index: 10000; padding: 10px 15px;">
                <i data-feather="aperture" style="margin-right: 8px;"></i> 体验液态玻璃
            </button>
        </main>

        <!-- 右侧信息面板 -->
        <aside class="sidebar right-sidebar" id="info-panel" aria-labelledby="info-panel-title">
            <button class="close-info-panel" aria-label="关闭信息面板">
                <i data-feather="x"></i>
            </button>
            <div class="info-panel-content">
                <div class="info-preview">
                    <!-- Icon will be dynamically set by JS -->
                    <i data-feather="file" class="preview-icon-large"></i>
                </div>
                <h3 id="info-panel-title" class="info-title">文件名</h3>
                <div class="info-details">
                    <p><strong>类型:</strong> <span id="info-type">未知</span></p>
                    <p><strong>大小:</strong> <span id="info-size">未知</span></p>
                    <p><strong>修改日期:</strong> <span id="info-modified">未知</span></p>
                    <p><strong>创建日期:</strong> <span id="info-created">未知</span></p>
                    <p><strong>路径:</strong> <span id="info-path">未知</span></p>
                </div>
                <div class="info-actions">
                    <button class="action-button"><i data-feather="download"></i> 下载</button>
                    <button class="action-button"><i data-feather="share-2"></i> 分享</button>
                    <button class="action-button"><i data-feather="edit-2"></i> 重命名</button>
                    <button class="action-button danger"><i data-feather="trash-2"></i> 删除</button>
                </div>
            </div>
        </aside>
    </div>

    <!-- JavaScript files -->
    <script src="liquid-glass-effect.js"></script> <!-- Your provided Liquid Glass effect script -->
    <script src="script.js"></script>             <!-- Custom interactions and logic -->
    <script>
        // Initialize Feather Icons
        if (typeof feather !== 'undefined') {
            feather.replace();
        } else {
            console.warn('Feather Icons script not loaded.');
        }
    </script>
</body>
</html>