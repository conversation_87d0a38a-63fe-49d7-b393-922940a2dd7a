/* CloudDrive 移动端现代化UI */

/* 重置和基础样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
  background: #f8f9fa;
  color: #333;
  line-height: 1.5;
  overflow: hidden;
  -webkit-user-select: none;
  user-select: none;
}

/* 状态栏 */
.status-bar {
  height: 44px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  font-size: 14px;
  font-weight: 600;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
}

.status-left .time {
  color: #333;
}

.status-center .notch {
  width: 120px;
  height: 20px;
  background: #333;
  border-radius: 10px;
}

.status-right {
  display: flex;
  align-items: center;
  gap: 4px;
}

.status-right i {
  color: #333;
  font-size: 12px;
}

/* 主应用容器 */
.mobile-app {
  padding-top: 44px;
  padding-bottom: 80px;
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 顶部导航栏 */
.mobile-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
}

.menu-btn,
.search-btn {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  background: rgba(0, 123, 255, 0.1);
  border: none;
  color: #007bff;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  cursor: pointer;
}

.menu-btn:active,
.search-btn:active {
  transform: scale(0.95);
  background: rgba(0, 123, 255, 0.2);
}

.header-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.header-icon {
  color: #007bff;
  font-size: 20px;
}

/* 面包屑导航 */
.mobile-breadcrumb {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.breadcrumb-content {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  gap: 8px;
  font-size: 14px;
}

.breadcrumb-item {
  color: #6c757d;
  text-decoration: none;
  transition: color 0.2s ease;
}

.breadcrumb-item:active {
  color: #007bff;
}

.breadcrumb-current {
  color: #333;
  font-weight: 500;
}

.breadcrumb-separator {
  color: #adb5bd;
  font-size: 10px;
}

/* 快速操作栏 */
.quick-actions {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  padding: 12px 0;
}

.actions-scroll {
  display: flex;
  gap: 12px;
  padding: 0 16px;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

.actions-scroll::-webkit-scrollbar {
  display: none;
}

.quick-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  min-width: 70px;
  transition: all 0.3s ease;
  white-space: nowrap;
  cursor: pointer;
}

.quick-btn:active {
  transform: scale(0.95);
  background: rgba(0, 123, 255, 0.1);
}

.quick-btn i {
  font-size: 18px;
  color: #007bff;
  margin-bottom: 4px;
}

.quick-btn span {
  font-size: 12px;
  color: #333;
  font-weight: 500;
}

/* 文件容器 */
.file-container {
  flex: 1;
  overflow: hidden;
  background: #ffffff;
}

.file-list-mobile {
  height: 100%;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  padding: 8px 0;
}

/* 移动端文件项 */
.mobile-file-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background: #ffffff;
  border-bottom: 1px solid #f1f3f4;
  transition: all 0.3s ease;
  cursor: pointer;
}

.mobile-file-item:active {
  background: rgba(0, 123, 255, 0.05);
  transform: scale(0.98);
}

.mobile-file-item:last-child {
  border-bottom: none;
}

.file-icon-wrapper {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  flex-shrink: 0;
}

.file-icon {
  font-size: 24px;
}

.mobile-file-item.folder .file-icon {
  color: #ffc107;
}

.mobile-file-item.file .file-icon {
  color: #6c757d;
}

.file-details {
  flex: 1;
  min-width: 0;
}

.file-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.file-meta {
  font-size: 12px;
  color: #6c757d;
}

.file-more-btn {
  width: 32px;
  height: 32px;
  border-radius: 16px;
  background: rgba(0, 0, 0, 0.05);
  border: none;
  color: #6c757d;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  cursor: pointer;
  flex-shrink: 0;
}

.file-more-btn:active {
  transform: scale(0.9);
  background: rgba(0, 0, 0, 0.1);
}

/* 底部导航 */
.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  display: flex;
  padding: 8px 0 calc(8px + env(safe-area-inset-bottom));
  z-index: 1000;
}

.nav-tab {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px;
  color: #6c757d;
  text-decoration: none;
  transition: all 0.3s ease;
}

.nav-tab.active {
  color: #007bff;
}

.nav-tab:active {
  transform: scale(0.95);
}

.nav-tab i {
  font-size: 20px;
  margin-bottom: 4px;
}

.nav-tab span {
  font-size: 10px;
  font-weight: 500;
}

/* 侧边菜单 */
.mobile-sidebar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2000;
  visibility: hidden;
  opacity: 0;
  transition: all 0.3s ease;
}

.mobile-sidebar.open {
  visibility: visible;
  opacity: 1;
}

.sidebar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
}

.sidebar-content {
  position: absolute;
  top: 0;
  left: 0;
  width: 280px;
  height: 100%;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  transform: translateX(-100%);
  transition: transform 0.3s ease;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

.mobile-sidebar.open .sidebar-content {
  transform: translateX(0);
}

.sidebar-header {
  padding: 60px 20px 20px;
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.user-profile {
  display: flex;
  align-items: center;
}

.user-avatar {
  width: 48px;
  height: 48px;
  border-radius: 24px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 20px;
}

.user-name {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 2px;
}

.user-email {
  font-size: 12px;
  opacity: 0.8;
}

.close-sidebar {
  width: 32px;
  height: 32px;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.sidebar-menu {
  padding: 20px 0;
}

.menu-section {
  margin-bottom: 24px;
}

.section-title {
  font-size: 12px;
  font-weight: 500;
  color: #6c757d;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 8px;
  padding: 0 20px;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  color: #333;
  text-decoration: none;
  transition: all 0.3s ease;
}

.menu-item:active {
  background: rgba(0, 123, 255, 0.05);
}

.menu-item i {
  width: 24px;
  margin-right: 12px;
  color: #007bff;
  font-size: 16px;
}

/* 云盘图标 */
.cloud-icon {
  width: 24px;
  height: 24px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  color: white;
  margin-right: 12px;
  flex-shrink: 0;
}

.cloud-icon.aliyun {
  background: linear-gradient(135deg, #ff6b35, #f7931e);
}

.cloud-icon.baidu {
  background: linear-gradient(135deg, #2932e1, #1e88e5);
}

.cloud-icon.onedrive {
  background: linear-gradient(135deg, #0078d4, #106ebe);
}

/* 状态指示器 */
.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-left: auto;
  flex-shrink: 0;
}

.status-dot.online {
  background: #28a745;
}

.status-dot.offline {
  background: #dc3545;
}

/* 存储空间 */
.storage-summary {
  padding: 16px 20px;
  background: rgba(0, 123, 255, 0.05);
  margin: 0 20px;
  border-radius: 12px;
}

.storage-text {
  font-size: 12px;
  color: #6c757d;
  margin-bottom: 8px;
}

.storage-bar {
  height: 4px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 2px;
  overflow: hidden;
}

.storage-fill {
  height: 100%;
  background: linear-gradient(90deg, #007bff, #0056b3);
  border-radius: 2px;
  transition: width 0.3s ease;
}

/* 搜索覆盖层 */
.search-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  z-index: 3000;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.search-overlay.open {
  transform: translateY(0);
}

.search-header {
  display: flex;
  align-items: center;
  padding: 60px 20px 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.back-btn {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  background: rgba(0, 123, 255, 0.1);
  border: none;
  color: #007bff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  cursor: pointer;
}

.search-input-wrapper {
  flex: 1;
  position: relative;
}

.search-input-wrapper i {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: #6c757d;
}

.search-input {
  width: 100%;
  padding: 12px 16px 12px 48px;
  background: rgba(0, 0, 0, 0.05);
  border: none;
  border-radius: 20px;
  font-size: 16px;
  outline: none;
}

.search-content {
  padding: 20px;
}

.search-suggestions h4 {
  font-size: 14px;
  font-weight: 600;
  color: #6c757d;
  margin-bottom: 16px;
}

.suggestion-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  color: #333;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.suggestion-item i {
  width: 20px;
  margin-right: 12px;
  color: #6c757d;
}

/* 操作菜单 */
.action-sheet {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 4000;
  visibility: hidden;
  opacity: 0;
  transition: all 0.3s ease;
}

.action-sheet.open {
  visibility: visible;
  opacity: 1;
}

.action-sheet-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
}

.action-sheet-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
  transform: translateY(100%);
  transition: transform 0.3s ease;
  max-height: 60vh;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

.action-sheet.open .action-sheet-content {
  transform: translateY(0);
}

.action-sheet-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.action-sheet-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.close-action-sheet {
  width: 32px;
  height: 32px;
  border-radius: 16px;
  background: rgba(0, 0, 0, 0.05);
  border: none;
  color: #6c757d;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.action-sheet-body {
  padding: 0 20px 40px;
}

.action-item {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 16px 0;
  background: none;
  border: none;
  color: #333;
  font-size: 16px;
  text-align: left;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  cursor: pointer;
}

.action-item:active {
  background: rgba(0, 123, 255, 0.05);
  margin: 0 -20px;
  padding-left: 20px;
  padding-right: 20px;
}

.action-item.danger {
  color: #dc3545;
}

.action-item i {
  width: 24px;
  margin-right: 16px;
  font-size: 16px;
}

/* 安全区域适配 */
@supports (padding: max(0px)) {
  .status-bar {
    padding-top: max(0px, env(safe-area-inset-top));
    height: calc(44px + env(safe-area-inset-top));
  }
  
  .mobile-app {
    padding-top: calc(44px + env(safe-area-inset-top));
  }
  
  .bottom-nav {
    padding-bottom: max(8px, calc(8px + env(safe-area-inset-bottom)));
  }
  
  .search-header {
    padding-top: calc(60px + env(safe-area-inset-top));
  }
  
  .sidebar-header {
    padding-top: calc(60px + env(safe-area-inset-top));
  }
}
