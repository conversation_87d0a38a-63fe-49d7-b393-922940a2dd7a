/* 移动端专用样式 */

/* 状态栏 */
.status-bar {
  height: 44px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  font-size: 14px;
  font-weight: 600;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.status-left .time {
  color: #1d1d1f;
}

.status-center .notch {
  width: 120px;
  height: 20px;
  background: #1d1d1f;
  border-radius: 10px;
}

.status-right {
  display: flex;
  align-items: center;
  space-x: 4px;
}

.status-right i {
  margin-left: 4px;
  color: #1d1d1f;
}

/* 移动端头部 */
.mobile-header {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.menu-btn, .search-btn {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  background: rgba(0, 122, 255, 0.1);
  border: none;
  color: #007AFF;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.menu-btn:active, .search-btn:active {
  transform: scale(0.95);
  background: rgba(0, 122, 255, 0.2);
}

.header-title {
  font-size: 18px;
  font-weight: 600;
  color: #1d1d1f;
}

/* 快速操作栏 */
.quick-actions {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.quick-action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  min-width: 70px;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.quick-action-btn:active {
  transform: scale(0.95);
  background: rgba(0, 122, 255, 0.1);
}

.quick-action-btn i {
  font-size: 18px;
  color: #007AFF;
  margin-bottom: 4px;
}

.quick-action-btn span {
  font-size: 12px;
  color: #1d1d1f;
  font-weight: 500;
}

/* 云盘状态卡片 */
.cloud-status-cards {
  padding: 8px 0;
  background: rgba(255, 255, 255, 0.5);
}

.cloud-status-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
  min-width: 160px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

/* 移动端文件列表 */
.file-list-mobile {
  padding: 16px;
  overflow-y: auto;
  height: 100%;
}

.mobile-file-item {
  display: flex;
  align-items: center;
  padding: 16px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16px;
  margin-bottom: 12px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.mobile-file-item:active {
  transform: scale(0.98);
  background: rgba(0, 122, 255, 0.05);
}

.file-icon-mobile {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 20px;
  color: white;
}

.mobile-file-item.folder .file-icon-mobile {
  background: linear-gradient(135deg, #FFD60A, #FF9500);
}

.mobile-file-item.image .file-icon-mobile {
  background: linear-gradient(135deg, #FF6B35, #F7931E);
}

.mobile-file-item.video .file-icon-mobile {
  background: linear-gradient(135deg, #FF3B30, #D70015);
}

.mobile-file-item.document .file-icon-mobile {
  background: linear-gradient(135deg, #007AFF, #5856D6);
}

.mobile-file-item.audio .file-icon-mobile {
  background: linear-gradient(135deg, #AF52DE, #FF2D92);
}

.mobile-file-item.archive .file-icon-mobile {
  background: linear-gradient(135deg, #8E8E93, #636366);
}

.file-info-mobile {
  flex: 1;
}

.file-name-mobile {
  font-size: 16px;
  font-weight: 600;
  color: #1d1d1f;
  margin-bottom: 4px;
  line-height: 1.2;
}

.file-meta-mobile {
  font-size: 14px;
  color: #8e8e93;
  line-height: 1.2;
}

.file-menu-btn {
  width: 32px;
  height: 32px;
  border-radius: 16px;
  background: rgba(0, 0, 0, 0.05);
  border: none;
  color: #8e8e93;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.file-menu-btn:active {
  transform: scale(0.9);
  background: rgba(0, 0, 0, 0.1);
}

/* 底部导航 */
.mobile-bottom-nav {
  display: flex;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  padding: 8px 0 calc(8px + env(safe-area-inset-bottom));
  box-shadow: 0 -2px 16px rgba(0, 0, 0, 0.1);
}

.nav-item-mobile {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px;
  color: #8e8e93;
  text-decoration: none;
  transition: all 0.3s ease;
}

.nav-item-mobile.active {
  color: #007AFF;
}

.nav-item-mobile:active {
  transform: scale(0.95);
}

.nav-item-mobile i {
  font-size: 20px;
  margin-bottom: 4px;
}

.nav-item-mobile span {
  font-size: 10px;
  font-weight: 500;
}

/* 侧边菜单 */
.mobile-sidebar {
  position: fixed;
  top: 0;
  left: -100%;
  width: 280px;
  height: 100%;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  z-index: 1000;
  transition: left 0.3s ease;
  box-shadow: 2px 0 16px rgba(0, 0, 0, 0.1);
}

.mobile-sidebar.open {
  left: 0;
}

.sidebar-header {
  padding: 60px 20px 20px;
  background: linear-gradient(135deg, #007AFF, #5856D6);
  color: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.user-info {
  display: flex;
  align-items: center;
}

.user-avatar {
  width: 48px;
  height: 48px;
  border-radius: 24px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 20px;
}

.user-name {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 2px;
}

.user-email {
  font-size: 12px;
  opacity: 0.8;
}

.close-sidebar-btn {
  width: 32px;
  height: 32px;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sidebar-content {
  padding: 20px;
}

.sidebar-nav-item {
  display: flex;
  align-items: center;
  padding: 16px 0;
  color: #1d1d1f;
  text-decoration: none;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.sidebar-nav-item:active {
  background: rgba(0, 122, 255, 0.05);
  margin: 0 -20px;
  padding-left: 20px;
  padding-right: 20px;
}

.sidebar-nav-item i {
  width: 24px;
  margin-right: 16px;
  color: #007AFF;
}

.storage-info {
  margin-top: 24px;
  padding: 16px;
  background: rgba(0, 122, 255, 0.05);
  border-radius: 12px;
}

.storage-info h4 {
  font-size: 14px;
  font-weight: 600;
  color: #1d1d1f;
  margin-bottom: 8px;
}

.storage-bar-mobile {
  width: 100%;
  height: 6px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 8px;
}

.storage-progress-mobile {
  height: 100%;
  background: linear-gradient(90deg, #007AFF, #5856D6);
  border-radius: 3px;
  transition: width 0.5s ease;
}

.storage-text {
  font-size: 12px;
  color: #8e8e93;
}

/* 搜索覆盖层 */
.search-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  z-index: 2000;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.search-overlay.open {
  transform: translateY(0);
}

.search-header {
  display: flex;
  align-items: center;
  padding: 60px 20px 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.back-btn {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  background: rgba(0, 122, 255, 0.1);
  border: none;
  color: #007AFF;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
}

.search-input-container {
  flex: 1;
  position: relative;
}

.search-input-container i {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: #8e8e93;
}

.search-input-mobile {
  width: 100%;
  padding: 12px 16px 12px 48px;
  background: rgba(0, 0, 0, 0.05);
  border: none;
  border-radius: 20px;
  font-size: 16px;
  outline: none;
}

.search-suggestions {
  padding: 20px;
}

.search-suggestions h4 {
  font-size: 14px;
  font-weight: 600;
  color: #8e8e93;
  margin-bottom: 16px;
}

.suggestion-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  color: #1d1d1f;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.suggestion-item i {
  width: 20px;
  margin-right: 12px;
  color: #8e8e93;
}

/* 文件操作菜单 */
.file-action-menu {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
  z-index: 3000;
  transform: translateY(100%);
  transition: transform 0.3s ease;
  box-shadow: 0 -8px 32px rgba(0, 0, 0, 0.1);
}

.file-action-menu.open {
  transform: translateY(0);
}

.action-menu-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.action-menu-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #1d1d1f;
}

.close-menu-btn {
  width: 32px;
  height: 32px;
  border-radius: 16px;
  background: rgba(0, 0, 0, 0.05);
  border: none;
  color: #8e8e93;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-menu-content {
  padding: 0 20px 40px;
}

.action-menu-item {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 16px 0;
  background: none;
  border: none;
  color: #1d1d1f;
  font-size: 16px;
  text-align: left;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.action-menu-item:active {
  background: rgba(0, 122, 255, 0.05);
  margin: 0 -20px;
  padding-left: 20px;
  padding-right: 20px;
}

.action-menu-item.danger {
  color: #FF3B30;
}

.action-menu-item i {
  width: 24px;
  margin-right: 16px;
}

/* 遮罩层 */
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 999;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.overlay.show {
  opacity: 1;
  visibility: visible;
}

/* 触摸反馈 */
@media (hover: none) and (pointer: coarse) {
  .mobile-file-item:hover {
    transform: none;
  }
  
  .quick-action-btn:hover,
  .nav-item-mobile:hover,
  .sidebar-nav-item:hover,
  .action-menu-item:hover {
    background: none;
  }
}

/* 安全区域适配 */
@supports (padding: max(0px)) {
  .status-bar {
    padding-top: max(44px, env(safe-area-inset-top));
  }
  
  .mobile-bottom-nav {
    padding-bottom: max(8px, env(safe-area-inset-bottom));
  }
  
  .search-header {
    padding-top: max(60px, calc(44px + env(safe-area-inset-top)));
  }
  
  .sidebar-header {
    padding-top: max(60px, calc(44px + env(safe-area-inset-top)));
  }
}
