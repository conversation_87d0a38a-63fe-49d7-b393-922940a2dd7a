/* CloudDrive 现代化UI - 基于参考图片设计 */

/* 重置和基础样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  background: #f8f9fa;
  color: #333;
  line-height: 1.5;
  overflow: hidden;
}

/* 主容器 */
.app-container {
  display: flex;
  height: 100vh;
  background: #ffffff;
}

/* 左侧边栏 */
.sidebar {
  width: 240px;
  background: #ffffff;
  border-right: 1px solid #e9ecef;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 侧边栏头部 */
.sidebar-header {
  padding: 20px 16px;
  border-bottom: 1px solid #e9ecef;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 8px;
}

.logo-icon {
  width: 24px;
  height: 24px;
  color: #007bff;
  font-size: 20px;
}

.logo-text {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

/* 导航菜单 */
.nav-menu {
  flex: 1;
  padding: 16px 0;
  overflow-y: auto;
}

.nav-section {
  margin-bottom: 24px;
}

.nav-section-title {
  font-size: 12px;
  font-weight: 500;
  color: #6c757d;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 8px;
  padding: 0 16px;
}

.nav-list {
  list-style: none;
}

.nav-item {
  margin-bottom: 2px;
}

.nav-link {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  color: #495057;
  text-decoration: none;
  border-radius: 6px;
  margin: 0 8px;
  transition: all 0.2s ease;
  position: relative;
}

.nav-link:hover {
  background: #f8f9fa;
  color: #007bff;
}

.nav-item.active .nav-link {
  background: #e3f2fd;
  color: #007bff;
  font-weight: 500;
}

.nav-icon {
  width: 16px;
  margin-right: 12px;
  font-size: 14px;
}

/* 云盘图标 */
.cloud-icon {
  width: 20px;
  height: 20px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  color: white;
  margin-right: 12px;
  flex-shrink: 0;
}

.cloud-icon.aliyun {
  background: linear-gradient(135deg, #ff6b35, #f7931e);
}

.cloud-icon.baidu {
  background: linear-gradient(135deg, #2932e1, #1e88e5);
}

.cloud-icon.onedrive {
  background: linear-gradient(135deg, #0078d4, #106ebe);
}

.cloud-icon.webdav {
  background: linear-gradient(135deg, #28a745, #20c997);
}

/* 状态指示器 */
.status-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  margin-left: auto;
  flex-shrink: 0;
}

.status-dot.online {
  background: #28a745;
}

.status-dot.offline {
  background: #dc3545;
}

/* 存储空间信息 */
.storage-info {
  padding: 16px;
  margin: 0 8px 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.storage-title {
  font-size: 12px;
  font-weight: 500;
  color: #6c757d;
  margin-bottom: 8px;
}

.storage-label {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  margin-bottom: 6px;
}

.storage-usage {
  color: #6c757d;
}

.storage-bar {
  height: 4px;
  background: #e9ecef;
  border-radius: 2px;
  overflow: hidden;
}

.storage-progress {
  height: 100%;
  background: linear-gradient(90deg, #007bff, #0056b3);
  border-radius: 2px;
  transition: width 0.3s ease;
}

/* 主内容区域 */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: #ffffff;
}

/* 顶部工具栏 */
.toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 20px;
  border-bottom: 1px solid #e9ecef;
  background: #ffffff;
  min-height: 60px;
}

.toolbar-left,
.toolbar-center,
.toolbar-right {
  display: flex;
  align-items: center;
}

.toolbar-center {
  flex: 1;
  justify-content: center;
  max-width: 400px;
  margin: 0 20px;
}

/* 面包屑导航 */
.breadcrumb {
  display: flex;
  align-items: center;
  gap: 8px;
}

.breadcrumb-item {
  color: #6c757d;
  text-decoration: none;
  font-size: 14px;
  transition: color 0.2s ease;
}

.breadcrumb-item:hover {
  color: #007bff;
}

.breadcrumb-current {
  color: #333;
  font-weight: 500;
  font-size: 14px;
}

.breadcrumb-separator {
  color: #adb5bd;
  font-size: 10px;
}

/* 搜索框 */
.search-box {
  position: relative;
  width: 100%;
  max-width: 300px;
}

.search-input {
  width: 100%;
  padding: 8px 16px 8px 36px;
  border: 1px solid #dee2e6;
  border-radius: 20px;
  background: #f8f9fa;
  font-size: 14px;
  outline: none;
  transition: all 0.2s ease;
}

.search-input:focus {
  border-color: #007bff;
  background: #ffffff;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #6c757d;
  font-size: 14px;
}

/* 视图切换 */
.view-toggle {
  display: flex;
  background: #f8f9fa;
  border-radius: 6px;
  padding: 2px;
  margin-right: 12px;
}

.view-btn {
  padding: 6px 10px;
  border: none;
  background: transparent;
  color: #6c757d;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
}

.view-btn.active {
  background: #ffffff;
  color: #007bff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 8px;
}

.action-btn {
  padding: 8px 12px;
  border: 1px solid #dee2e6;
  background: #ffffff;
  color: #495057;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.action-btn:hover {
  border-color: #007bff;
  color: #007bff;
  background: #f8f9fa;
}

/* 文件区域 */
.file-area {
  flex: 1;
  overflow: hidden;
  background: #ffffff;
}

.file-list {
  height: 100%;
  overflow-y: auto;
}

/* 文件列表头部 */
.file-header {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  font-size: 12px;
  font-weight: 500;
  color: #6c757d;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.file-col {
  display: flex;
  align-items: center;
}

.file-col-name {
  flex: 1;
  min-width: 0;
}

.file-col-size {
  width: 100px;
  justify-content: flex-end;
}

.file-col-date {
  width: 150px;
  justify-content: flex-end;
}

.sort-icon {
  margin-left: 4px;
  font-size: 10px;
  color: #adb5bd;
}

/* 文件项 */
.file-item {
  display: flex;
  align-items: center;
  padding: 10px 20px;
  border-bottom: 1px solid #f1f3f4;
  cursor: pointer;
  transition: all 0.2s ease;
}

.file-item:hover {
  background: #f8f9fa;
}

.file-item:last-child {
  border-bottom: none;
}

.file-item.selected {
  background: #e3f2fd;
}

/* 文件图标 */
.file-icon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  flex-shrink: 0;
}

.file-item.folder .file-icon {
  color: #ffc107;
}

.file-item.file .file-icon {
  color: #6c757d;
}

/* 文件名 */
.file-name {
  font-size: 14px;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 文件大小和日期 */
.file-size,
.file-date {
  font-size: 12px;
  color: #6c757d;
}

/* 右键菜单 */
.context-menu {
  position: fixed;
  background: #ffffff;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 4px 0;
  min-width: 150px;
  z-index: 1000;
  display: none;
}

.context-menu-item {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  cursor: pointer;
  transition: background 0.2s ease;
  font-size: 14px;
}

.context-menu-item:hover {
  background: #f8f9fa;
}

.context-menu-item.danger {
  color: #dc3545;
}

.context-menu-item i {
  width: 16px;
  margin-right: 12px;
  font-size: 12px;
}

.context-menu-divider {
  height: 1px;
  background: #e9ecef;
  margin: 4px 0;
}

/* 液态玻璃效果增强 */
.sidebar {
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.toolbar {
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.file-item {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.file-item:hover {
  background: rgba(0, 123, 255, 0.05);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.nav-link {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.nav-link:hover {
  transform: translateX(4px);
  background: linear-gradient(90deg, rgba(0, 123, 255, 0.1), transparent);
}

.action-btn {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.action-btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle, rgba(0, 123, 255, 0.2) 0%, transparent 70%);
  transition: all 0.6s ease;
  transform: translate(-50%, -50%);
  border-radius: 50%;
}

.action-btn:hover::before {
  width: 200px;
  height: 200px;
}

.action-btn:active {
  transform: scale(0.98);
}

.search-input {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.search-input:focus {
  background: rgba(255, 255, 255, 0.9);
  transform: scale(1.02);
}

.storage-info {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  background: rgba(248, 249, 250, 0.8);
  transition: all 0.3s ease;
}

.storage-info:hover {
  background: rgba(248, 249, 250, 0.95);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar {
    position: fixed;
    left: -240px;
    top: 0;
    height: 100%;
    z-index: 1000;
    transition: left 0.3s ease;
  }

  .sidebar.open {
    left: 0;
  }

  .toolbar {
    padding: 8px 16px;
  }

  .toolbar-center {
    margin: 0 12px;
  }

  .file-col-size,
  .file-col-date {
    display: none;
  }

  .action-buttons .action-btn span {
    display: none;
  }
}
