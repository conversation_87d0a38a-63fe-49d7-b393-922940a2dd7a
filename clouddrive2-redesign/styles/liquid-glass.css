/* 液态玻璃效果 - iOS 16风格 */

/* 液态玻璃基础效果 */
.liquid-glass {
  position: relative;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.1),
    rgba(255, 255, 255, 0.05)
  );
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.18);
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* 液态玻璃动态效果 */
.liquid-glass::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    45deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.liquid-glass:hover::before {
  opacity: 1;
  animation: liquid-flow 2s ease-in-out infinite;
}

@keyframes liquid-flow {
  0%, 100% {
    transform: translateX(-100%) skewX(-15deg);
  }
  50% {
    transform: translateX(100%) skewX(-15deg);
  }
}

/* 液态玻璃变体 */
.liquid-glass-primary {
  background: linear-gradient(
    135deg,
    rgba(0, 122, 255, 0.15),
    rgba(88, 86, 214, 0.1)
  );
  border: 1px solid rgba(0, 122, 255, 0.2);
}

.liquid-glass-secondary {
  background: linear-gradient(
    135deg,
    rgba(88, 86, 214, 0.15),
    rgba(255, 45, 85, 0.1)
  );
  border: 1px solid rgba(88, 86, 214, 0.2);
}

.liquid-glass-success {
  background: linear-gradient(
    135deg,
    rgba(52, 199, 89, 0.15),
    rgba(48, 176, 199, 0.1)
  );
  border: 1px solid rgba(52, 199, 89, 0.2);
}

/* 液态按钮效果 */
.liquid-button {
  position: relative;
  overflow: hidden;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.2),
    rgba(255, 255, 255, 0.1)
  );
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.liquid-button::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(
    circle,
    rgba(255, 255, 255, 0.3) 0%,
    transparent 70%
  );
  transition: all 0.6s ease;
  transform: translate(-50%, -50%);
  border-radius: 50%;
}

.liquid-button:hover::before {
  width: 300px;
  height: 300px;
}

.liquid-button:active {
  transform: scale(0.98);
}

/* 液态卡片效果 */
.liquid-card {
  position: relative;
  background: linear-gradient(
    145deg,
    rgba(255, 255, 255, 0.8),
    rgba(255, 255, 255, 0.6)
  );
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.liquid-card::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: conic-gradient(
    from 0deg,
    transparent,
    rgba(0, 122, 255, 0.1),
    transparent,
    rgba(88, 86, 214, 0.1),
    transparent
  );
  animation: liquid-rotate 8s linear infinite;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.liquid-card:hover::after {
  opacity: 1;
}

.liquid-card:hover {
  transform: translateY(-8px);
  box-shadow: 
    0 20px 40px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(255, 255, 255, 0.2);
}

@keyframes liquid-rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 液态输入框效果 */
.liquid-input {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.liquid-input::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(0, 122, 255, 0.1),
    transparent
  );
  transition: left 0.5s ease;
}

.liquid-input:focus::before {
  left: 100%;
}

.liquid-input:focus {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(0, 122, 255, 0.3);
  box-shadow: 
    0 0 0 3px rgba(0, 122, 255, 0.1),
    0 8px 16px rgba(0, 0, 0, 0.1);
}

/* 液态导航效果 */
.liquid-nav-item {
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.liquid-nav-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(0, 122, 255, 0.1),
    rgba(88, 86, 214, 0.1),
    transparent
  );
  transition: left 0.4s ease;
}

.liquid-nav-item:hover::before {
  left: 100%;
}

.liquid-nav-item.active {
  background: linear-gradient(
    135deg,
    rgba(0, 122, 255, 0.15),
    rgba(88, 86, 214, 0.1)
  );
}

/* 液态加载效果 */
.liquid-loading {
  position: relative;
  overflow: hidden;
}

.liquid-loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  animation: liquid-loading 1.5s ease-in-out infinite;
}

@keyframes liquid-loading {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* 液态悬浮效果 */
.liquid-float {
  animation: liquid-float 3s ease-in-out infinite;
}

@keyframes liquid-float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

/* 液态脉冲效果 */
.liquid-pulse {
  position: relative;
}

.liquid-pulse::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  background: inherit;
  border-radius: inherit;
  transform: translate(-50%, -50%);
  animation: liquid-pulse 2s ease-in-out infinite;
  opacity: 0.7;
}

@keyframes liquid-pulse {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.7;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.05);
    opacity: 0.3;
  }
  100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.7;
  }
}

/* 液态波纹效果 */
.liquid-ripple {
  position: relative;
  overflow: hidden;
}

.liquid-ripple::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(
    circle,
    rgba(255, 255, 255, 0.3) 0%,
    rgba(255, 255, 255, 0.1) 50%,
    transparent 100%
  );
  transform: translate(-50%, -50%);
  transition: all 0.6s ease;
  border-radius: 50%;
  pointer-events: none;
}

.liquid-ripple:active::after {
  width: 200px;
  height: 200px;
  opacity: 0;
}

/* 液态边框效果 */
.liquid-border {
  position: relative;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.1),
    rgba(255, 255, 255, 0.05)
  );
}

.liquid-border::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 2px;
  background: linear-gradient(
    45deg,
    rgba(0, 122, 255, 0.5),
    rgba(88, 86, 214, 0.5),
    rgba(255, 45, 85, 0.5),
    rgba(0, 122, 255, 0.5)
  );
  background-size: 200% 200%;
  border-radius: inherit;
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
  animation: liquid-border 3s ease infinite;
}

@keyframes liquid-border {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

/* 响应式液态效果 */
@media (max-width: 768px) {
  .liquid-glass {
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
  }
  
  .liquid-card {
    border-radius: 16px;
  }
  
  .liquid-button::before {
    transition: all 0.4s ease;
  }
  
  .liquid-button:hover::before {
    width: 200px;
    height: 200px;
  }
}

/* 减少动画效果（用户偏好） */
@media (prefers-reduced-motion: reduce) {
  .liquid-glass::before,
  .liquid-button::before,
  .liquid-card::after,
  .liquid-input::before,
  .liquid-nav-item::before,
  .liquid-loading::after,
  .liquid-pulse::before,
  .liquid-ripple::after,
  .liquid-border::before {
    animation: none;
    transition: none;
  }
  
  .liquid-float {
    animation: none;
  }
}
