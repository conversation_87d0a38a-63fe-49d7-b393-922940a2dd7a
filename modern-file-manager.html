<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CloudDrive - 现代化文件管理器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .app-container {
            display: flex;
            min-height: 100vh;
            padding: 20px;
            gap: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 20px 30px;
            margin-bottom: 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            animation: slideInDown 0.8s ease-out;
        }

        .header h1 {
            color: white;
            font-size: 2.5rem;
            font-weight: 700;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
            margin-bottom: 10px;
        }

        .breadcrumb {
            display: flex;
            align-items: center;
            gap: 10px;
            color: rgba(255, 255, 255, 0.8);
            font-size: 1rem;
        }

        .breadcrumb-item {
            padding: 8px 16px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .breadcrumb-item:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        .toolbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            flex-wrap: wrap;
            gap: 15px;
        }

        .search-container {
            position: relative;
            flex: 1;
            max-width: 400px;
        }

        .search-input {
            width: 100%;
            padding: 15px 50px 15px 20px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            color: white;
            font-size: 1rem;
            outline: none;
            transition: all 0.3s ease;
        }

        .search-input::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .search-input:focus {
            background: rgba(255, 255, 255, 0.15);
            border-color: rgba(255, 255, 255, 0.4);
            box-shadow: 0 0 20px rgba(255, 255, 255, 0.2);
        }

        .view-controls {
            display: flex;
            gap: 10px;
        }

        .control-btn {
            padding: 12px 16px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 16px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1rem;
        }

        .control-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        .control-btn.active {
            background: rgba(255, 255, 255, 0.3);
            box-shadow: inset 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .file-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 25px;
            animation: fadeInUp 1s ease-out 0.3s both;
        }

        .file-card {
            background: rgba(255, 255, 255, 0.08);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.15);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .file-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
        }

        .file-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
            border-color: rgba(255, 255, 255, 0.3);
        }

        .file-card:hover::before {
            opacity: 1;
        }

        .file-icon {
            width: 60px;
            height: 60px;
            margin-bottom: 15px;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            background: linear-gradient(135deg, #ff6b6b, #feca57);
            color: white;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
        }

        .file-name {
            color: white;
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 8px;
            line-height: 1.4;
        }

        .file-meta {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .file-size {
            background: rgba(255, 255, 255, 0.1);
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 0.8rem;
        }

        @keyframes slideInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes liquidGlass {
            0% { transform: scale(1) rotate(0deg); }
            50% { transform: scale(1.05) rotate(2deg); }
            100% { transform: scale(1) rotate(0deg); }
        }

        .file-card:active {
            animation: liquidGlass 0.6s ease-in-out;
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }

            .header h1 {
                font-size: 2rem;
            }

            .toolbar {
                flex-direction: column;
                align-items: stretch;
            }

            .search-container {
                max-width: none;
            }

            .view-controls {
                justify-content: center;
            }

            .file-grid {
                grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
                gap: 20px;
            }

            .breadcrumb {
                flex-wrap: wrap;
            }
        }

        @media (max-width: 480px) {
            .file-grid {
                grid-template-columns: 1fr;
            }

            .header {
                padding: 15px 20px;
            }

            .file-card {
                padding: 20px;
            }
        }

        /* 高级液态玻璃效果 */
        .liquid-glass {
            position: relative;
            overflow: hidden;
        }

        .liquid-glass::after {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: conic-gradient(from 0deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            animation: rotate 4s linear infinite;
            pointer-events: none;
        }

        @keyframes rotate {
            to {
                transform: rotate(360deg);
            }
        }

        /* 色散效果 */
        .chromatic-aberration {
            position: relative;
        }

        .chromatic-aberration::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: inherit;
            filter: blur(1px);
            transform: translateX(1px);
            opacity: 0.3;
            mix-blend-mode: screen;
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header liquid-glass">
            <h1>CloudDrive 文件管理器</h1>
            <nav class="breadcrumb">
                <span class="breadcrumb-item">🏠 首页</span>
                <span>→</span>
                <span class="breadcrumb-item">📁 文档</span>
                <span>→</span>
                <span class="breadcrumb-item">📂 项目文件</span>
            </nav>
        </header>

        <div class="toolbar">
            <div class="search-container">
                <input type="text" class="search-input" placeholder="搜索文件和文件夹...">
            </div>
            <div class="view-controls">
                <button class="control-btn active">🔲 网格</button>
                <button class="control-btn">📋 列表</button>
                <button class="control-btn">⚙️ 设置</button>
            </div>
        </div>

        <div class="file-grid" id="fileGrid">
            <!-- 文件项将通过JavaScript动态生成 -->
        </div>
    </div>

    <script>
        // 模拟文件数据
        const files = [
            { name: '项目文档.pdf', size: '2.5 MB', type: 'pdf', icon: '📄', date: '2025-06-18' },
            { name: '设计稿', size: '156 MB', type: 'folder', icon: '📁', date: '2025-06-17' },
            { name: '演示视频.mp4', size: '45.2 MB', type: 'video', icon: '🎬', date: '2025-06-16' },
            { name: '数据分析.xlsx', size: '1.8 MB', type: 'excel', icon: '📊', date: '2025-06-15' },
            { name: '代码库', size: '89.3 MB', type: 'folder', icon: '📂', date: '2025-06-14' },
            { name: '会议记录.docx', size: '856 KB', type: 'word', icon: '📝', date: '2025-06-13' },
            { name: '产品原型图.sketch', size: '12.4 MB', type: 'design', icon: '🎨', date: '2025-06-12' },
            { name: '财务报表.pdf', size: '3.2 MB', type: 'pdf', icon: '📄', date: '2025-06-11' }
        ];

        // 渲染文件网格
        function renderFiles() {
            const fileGrid = document.getElementById('fileGrid');
            fileGrid.innerHTML = '';

            files.forEach((file, index) => {
                const fileCard = document.createElement('div');
                fileCard.className = 'file-card chromatic-aberration';
                fileCard.style.animationDelay = `${index * 0.1}s`;
                
                fileCard.innerHTML = `
                    <div class="file-icon">${file.icon}</div>
                    <div class="file-name">${file.name}</div>
                    <div class="file-meta">
                        <span>${file.date}</span>
                        <span class="file-size">${file.size}</span>
                    </div>
                `;

                // 添加交互效果
                fileCard.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-8px) scale(1.02)';
                });

                fileCard.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });

                fileCard.addEventListener('click', function() {
                    // 添加点击动画
                    this.style.animation = 'liquidGlass 0.6s ease-in-out';
                    setTimeout(() => {
                        this.style.animation = '';
                    }, 600);
                });

                fileGrid.appendChild(fileCard);
            });
        }

        // 搜索功能
        document.querySelector('.search-input').addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            const fileCards = document.querySelectorAll('.file-card');
            
            fileCards.forEach(card => {
                const fileName = card.querySelector('.file-name').textContent.toLowerCase();
                if (fileName.includes(searchTerm)) {
                    card.style.display = 'block';
                    card.style.animation = 'fadeInUp 0.3s ease-out';
                } else {
                    card.style.display = 'none';
                }
            });
        });

        // 视图控制
        document.querySelectorAll('.control-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelectorAll('.control-btn').forEach(b => b.classList.remove('active'));
                this.classList.add('active');
            });
        });

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            renderFiles();
            
            // 添加页面加载动画
            document.body.style.opacity = '0';
            setTimeout(() => {
                document.body.style.transition = 'opacity 0.8s ease-in-out';
                document.body.style.opacity = '1';
            }, 100);
        });

        // 响应式处理
        window.addEventListener('resize', function() {
            // 可以在这里添加响应式逻辑
        });
    </script>
</body>
</html>
