/* CloudDrive 2.0 主题样式 */
:root {
    /* CloudDrive 品牌色彩 */
    --cd-primary: #407BFF;
    --cd-primary-light: #5A8FFF;
    --cd-primary-dark: #2D5BDB;
    --cd-secondary: #7877C6;
    --cd-accent: #FF453A;
    --cd-success: #34C759;
    --cd-warning: #FFCC00;
    --cd-error: #FF3B30;

    /* 系统颜色 */
    --cd-bg-primary: rgba(16, 18, 27, 0.95);
    --cd-bg-secondary: rgba(28, 32, 44, 0.9);
    --cd-bg-tertiary: rgba(44, 48, 64, 0.85);
    --cd-text-primary: rgba(255, 255, 255, 0.95);
    --cd-text-secondary: rgba(255, 255, 255, 0.7);
    --cd-text-tertiary: rgba(255, 255, 255, 0.5);
    --cd-text-quaternary: rgba(255, 255, 255, 0.3);

    /* 间距系统 */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    --spacing-xxl: 48px;

    /* 字体系统 */
    --font-family: 'Inter', 'PingFang SC', 'Microsoft YaHei', -apple-system, BlinkMacSystemFont, sans-serif;
    --font-size-xs: 11px;
    --font-size-sm: 13px;
    --font-size-md: 15px;
    --font-size-lg: 17px;
    --font-size-xl: 20px;
    --font-size-xxl: 24px;

    /* 阴影系统 */
    --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.1);
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.12);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.18);
    --shadow-xl: 0 16px 32px rgba(0, 0, 0, 0.22);

    /* 圆角系统 */
    --radius-xs: 4px;
    --radius-sm: 6px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --radius-xl: 16px;
    --radius-xxl: 20px;
}

/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    background: linear-gradient(135deg,
        #1a1d29 0%,
        #2d3748 25%,
        #4a5568 50%,
        #2d3748 75%,
        #1a1d29 100%);
    color: var(--cd-text-primary);
    line-height: 1.5;
    overflow: hidden;
    min-height: 100vh;
    font-size: var(--font-size-md);
}

/* 背景容器 */
.background-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -2;
}

.gradient-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg,
        #1e2139 0%,
        #2d3561 25%,
        #4a5f8a 50%,
        #2d3561 75%,
        #1e2139 100%);
    animation: gradientShift 20s ease infinite;
}

@keyframes gradientShift {
    0%, 100% {
        background: linear-gradient(135deg,
            #1e2139 0%,
            #2d3561 25%,
            #4a5f8a 50%,
            #2d3561 75%,
            #1e2139 100%);
    }
    25% {
        background: linear-gradient(135deg,
            #2d3561 0%,
            #4a5f8a 25%,
            #1e2139 50%,
            #2d3561 75%,
            #4a5f8a 100%);
    }
    50% {
        background: linear-gradient(135deg,
            #4a5f8a 0%,
            #1e2139 25%,
            #2d3561 50%,
            #4a5f8a 75%,
            #1e2139 100%);
    }
    75% {
        background: linear-gradient(135deg,
            #2d3561 0%,
            #1e2139 25%,
            #4a5f8a 50%,
            #2d3561 75%,
            #1e2139 100%);
    }
}

/* 应用容器 */
.app-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
    padding: var(--spacing-md);
    gap: var(--spacing-md);
}

/* 标题栏 */
.title-bar {
    padding: var(--spacing-md) var(--spacing-lg);
    display: flex;
    align-items: center;
    justify-content: space-between;
    backdrop-filter: blur(20px) saturate(180%);
    -webkit-backdrop-filter: blur(20px) saturate(180%);
    border-radius: var(--radius-lg);
    min-height: 60px;
}

.title-bar-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    flex: 0 0 auto;
}

.window-controls {
    display: flex;
    gap: var(--spacing-sm);
}

.control-btn {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    cursor: pointer;
    transition: var(--transition-smooth);
}

.control-btn.close {
    background: #ff5f57;
}

.control-btn.minimize {
    background: #ffbd2e;
}

.control-btn.maximize {
    background: #28ca42;
}

.control-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.app-title {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--cd-text-primary);
}

.app-title i {
    font-size: var(--font-size-xl);
    color: var(--cd-primary);
}

.version {
    font-size: var(--font-size-xs);
    color: var(--cd-text-tertiary);
    background: rgba(255, 255, 255, 0.1);
    padding: 2px 6px;
    border-radius: var(--radius-xs);
}

.title-bar-center {
    flex: 1;
    display: flex;
    justify-content: center;
    max-width: 500px;
    margin: 0 var(--spacing-lg);
}

.search-container {
    width: 100%;
    position: relative;
}

.search-filter-btn {
    background: none;
    border: none;
    color: var(--cd-text-secondary);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-xs);
    transition: var(--transition-smooth);
}

.search-filter-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--cd-text-primary);
}

.title-bar-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    flex: 0 0 auto;
}

.toolbar-btn {
    position: relative;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--radius-md);
    background: transparent;
    border: none;
    color: var(--cd-text-secondary);
    cursor: pointer;
    transition: var(--transition-smooth);
}

.toolbar-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--cd-text-primary);
}

.notification-badge {
    position: absolute;
    top: -2px;
    right: -2px;
    background: var(--cd-error);
    color: white;
    font-size: 10px;
    font-weight: 600;
    padding: 2px 5px;
    border-radius: 8px;
    min-width: 16px;
    text-align: center;
}

.user-menu {
    position: relative;
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    border: 2px solid rgba(255, 255, 255, 0.2);
    cursor: pointer;
    transition: var(--transition-smooth);
}

.user-avatar:hover {
    border-color: var(--cd-primary);
    transform: scale(1.05);
}

/* 主内容区域 */
.main-content {
    flex: 1;
    display: grid;
    grid-template-columns: 280px 1fr 320px;
    gap: var(--spacing-md);
    min-height: 0;
    overflow: hidden;
}

/* 侧边栏 */
.sidebar {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
    padding: var(--spacing-lg);
    overflow-y: auto;
}

.sidebar-section h3 {
    font-size: var(--font-size-md);
    font-weight: 600;
    color: var(--cd-text-primary);
    margin-bottom: var(--spacing-md);
    padding-left: var(--spacing-xs);
}

/* 快速访问 */
.quick-access-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.quick-access-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    color: var(--cd-text-secondary);
    cursor: pointer;
    transition: var(--transition-smooth);
    font-size: var(--font-size-sm);
}

.quick-access-item:hover {
    background: rgba(255, 255, 255, 0.08);
    color: var(--cd-text-primary);
}

.quick-access-item.active {
    background: rgba(64, 123, 255, 0.15);
    color: var(--cd-primary);
    border: 1px solid rgba(64, 123, 255, 0.3);
}

.quick-access-item i {
    width: 16px;
    text-align: center;
    font-size: var(--font-size-sm);
}

/* 云盘列表 */
.cloud-drives-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
}

.cloud-drive-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    background: rgba(255, 255, 255, 0.04);
    border: 1px solid rgba(255, 255, 255, 0.08);
    cursor: pointer;
    transition: var(--transition-smooth);
}

.cloud-drive-item:hover {
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(255, 255, 255, 0.15);
    transform: translateY(-1px);
}

.drive-icon {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--radius-md);
    background: rgba(255, 255, 255, 0.1);
}

.drive-icon i {
    font-size: 18px;
}

.drive-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.drive-name {
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--cd-text-primary);
}

.drive-usage {
    font-size: var(--font-size-xs);
    color: var(--cd-text-tertiary);
}

.drive-status {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    flex-shrink: 0;
}

.drive-status.online {
    background: var(--cd-success);
    box-shadow: 0 0 8px rgba(52, 199, 89, 0.5);
}

.drive-status.syncing {
    background: var(--cd-warning);
    animation: pulse 2s infinite;
}

.drive-status.offline {
    background: var(--cd-text-quaternary);
}

.add-drive-btn {
    width: 100%;
    justify-content: center;
    padding: var(--spacing-md);
    font-size: var(--font-size-sm);
    border: 1px dashed rgba(255, 255, 255, 0.3);
    background: transparent;
}

.add-drive-btn:hover {
    border-style: solid;
    background: rgba(255, 255, 255, 0.05);
}

/* 存储统计 */
.storage-stats {
    padding: var(--spacing-md);
}

.total-storage {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.storage-label {
    font-size: var(--font-size-sm);
    color: var(--cd-text-secondary);
}

.storage-value {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--cd-text-primary);
}

.storage-breakdown {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.storage-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-xs);
}

.storage-color {
    width: 12px;
    height: 12px;
    border-radius: 2px;
    flex-shrink: 0;
}

.storage-item span:nth-child(2) {
    flex: 1;
    color: var(--cd-text-secondary);
}

.storage-item span:nth-child(3) {
    color: var(--cd-text-primary);
    font-weight: 500;
}

/* 文件区域 */
.file-area {
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* 工具栏 */
.toolbar {
    padding: var(--spacing-md) var(--spacing-lg);
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-md);
}

.toolbar-left {
    flex: 1;
}

.breadcrumb {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-sm);
}

.breadcrumb-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    color: var(--cd-text-secondary);
    cursor: pointer;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-xs);
    transition: var(--transition-smooth);
}

.breadcrumb-item:hover {
    background: rgba(255, 255, 255, 0.08);
    color: var(--cd-text-primary);
}

.breadcrumb-item.active {
    color: var(--cd-text-primary);
    font-weight: 500;
}

.breadcrumb-separator {
    color: var(--cd-text-quaternary);
    font-size: 10px;
}

.toolbar-center {
    flex: 0 0 auto;
}

.view-controls {
    display: flex;
    gap: var(--spacing-xs);
    background: rgba(255, 255, 255, 0.08);
    padding: var(--spacing-xs);
    border-radius: var(--radius-md);
}

.view-btn {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--radius-sm);
    background: transparent;
    border: none;
    color: var(--cd-text-secondary);
    cursor: pointer;
    transition: var(--transition-smooth);
}

.view-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--cd-text-primary);
}

.view-btn.active {
    background: rgba(64, 123, 255, 0.2);
    color: var(--cd-primary);
}

.toolbar-right {
    display: flex;
    gap: var(--spacing-sm);
    flex: 0 0 auto;
}

.action-btn {
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--radius-md);
    background: transparent;
    border: none;
    color: var(--cd-text-secondary);
    cursor: pointer;
    transition: var(--transition-smooth);
}

.action-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--cd-text-primary);
}

/* 文件列表容器 */
.file-list-container {
    flex: 1;
    overflow-y: auto;
    padding: 0 var(--spacing-lg) var(--spacing-lg);
}

.file-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: var(--spacing-md);
}

/* 文件项 */
.file-item {
    padding: var(--spacing-md);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    cursor: pointer;
    position: relative;
    min-height: 120px;
}

.file-item:hover .file-actions {
    opacity: 1;
}

.file-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 48px;
    margin-bottom: var(--spacing-sm);
}

.file-icon i {
    font-size: 32px;
}

.file-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
    text-align: center;
}

.file-name {
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--cd-text-primary);
    line-height: 1.3;
    word-break: break-word;
}

.file-meta {
    font-size: var(--font-size-xs);
    color: var(--cd-text-tertiary);
}

.file-actions {
    position: absolute;
    top: var(--spacing-sm);
    right: var(--spacing-sm);
    opacity: 0;
    transition: var(--transition-smooth);
}

.file-action-btn {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--radius-sm);
    background: rgba(0, 0, 0, 0.5);
    border: none;
    color: white;
    cursor: pointer;
    transition: var(--transition-smooth);
}

.file-action-btn:hover {
    background: rgba(0, 0, 0, 0.7);
    transform: scale(1.1);
}

/* 详情面板 */
.details-panel {
    padding: var(--spacing-lg);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
    overflow-y: auto;
}

.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.panel-header h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--cd-text-primary);
}

.panel-close-btn {
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--radius-sm);
    background: transparent;
    border: none;
    color: var(--cd-text-secondary);
    cursor: pointer;
    transition: var(--transition-smooth);
}

.panel-close-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--cd-text-primary);
}

.file-preview {
    aspect-ratio: 1;
    border-radius: var(--radius-md);
    background: rgba(255, 255, 255, 0.04);
    border: 1px solid rgba(255, 255, 255, 0.08);
    display: flex;
    align-items: center;
    justify-content: center;
}

.preview-placeholder {
    text-align: center;
    color: var(--cd-text-tertiary);
}

.preview-placeholder i {
    font-size: 48px;
    margin-bottom: var(--spacing-md);
    opacity: 0.5;
}

.file-properties {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.property-group h4 {
    font-size: var(--font-size-md);
    font-weight: 600;
    color: var(--cd-text-primary);
    margin-bottom: var(--spacing-md);
}

.property-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm) 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.property-item:last-child {
    border-bottom: none;
}

.property-label {
    font-size: var(--font-size-sm);
    color: var(--cd-text-secondary);
}

.property-value {
    font-size: var(--font-size-sm);
    color: var(--cd-text-primary);
    font-weight: 500;
}

.sharing-controls {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.share-btn, .link-btn {
    width: 100%;
    justify-content: center;
    padding: var(--spacing-md);
    font-size: var(--font-size-sm);
}

/* 状态栏 */
.status-bar {
    padding: var(--spacing-md) var(--spacing-lg);
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: var(--font-size-xs);
    min-height: 40px;
}

.status-left, .status-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.status-center {
    flex: 1;
    display: flex;
    justify-content: center;
}

.status-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    color: var(--cd-text-secondary);
}

.status-item i {
    font-size: 10px;
}

.sync-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    color: var(--cd-primary);
    font-weight: 500;
}

/* 上下文菜单 */
.context-menu {
    position: fixed;
    min-width: 180px;
    padding: var(--spacing-sm) 0;
    border-radius: var(--radius-md);
    z-index: 1000;
    display: none;
}

.context-menu.show {
    display: block;
    animation: contextMenuSlideIn 0.2s ease;
}

@keyframes contextMenuSlideIn {
    from {
        opacity: 0;
        transform: translateY(-8px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.menu-item {
    padding: var(--spacing-sm) var(--spacing-md);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    cursor: pointer;
    transition: var(--transition-smooth);
    font-size: var(--font-size-sm);
    color: var(--cd-text-secondary);
}

.menu-item:hover {
    background: rgba(255, 255, 255, 0.08);
    color: var(--cd-text-primary);
}

.menu-item.danger {
    color: var(--cd-error);
}

.menu-item.danger:hover {
    background: rgba(255, 59, 48, 0.1);
    color: var(--cd-error);
}

.menu-item i {
    width: 16px;
    text-align: center;
    font-size: var(--font-size-sm);
}

.menu-separator {
    height: 1px;
    background: rgba(255, 255, 255, 0.1);
    margin: var(--spacing-xs) 0;
}

/* 模态框 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 2000;
}

.modal-overlay.active {
    display: flex;
    animation: modalFadeIn 0.3s ease;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.modal {
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--cd-text-primary);
}

.modal-close {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--radius-sm);
    background: transparent;
    border: none;
    color: var(--cd-text-secondary);
    cursor: pointer;
    transition: var(--transition-smooth);
}

.modal-close:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--cd-text-primary);
}

.modal-content {
    padding: var(--spacing-lg);
    max-height: 60vh;
    overflow-y: auto;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .main-content {
        grid-template-columns: 260px 1fr 280px;
    }
}

@media (max-width: 1024px) {
    .main-content {
        grid-template-columns: 240px 1fr;
    }

    .details-panel {
        display: none;
    }
}

@media (max-width: 768px) {
    .app-container {
        padding: var(--spacing-sm);
        gap: var(--spacing-sm);
    }

    .title-bar {
        padding: var(--spacing-sm) var(--spacing-md);
        flex-wrap: wrap;
        gap: var(--spacing-sm);
    }

    .title-bar-center {
        order: 3;
        flex: 1 1 100%;
        margin: 0;
    }

    .main-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
    }

    .sidebar {
        order: 2;
        padding: var(--spacing-md);
    }

    .file-area {
        order: 1;
    }

    .toolbar {
        padding: var(--spacing-sm) var(--spacing-md);
        flex-wrap: wrap;
        gap: var(--spacing-sm);
    }

    .toolbar-center {
        order: 3;
        flex: 1 1 100%;
    }

    .file-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: var(--spacing-sm);
    }

    .file-list-container {
        padding: 0 var(--spacing-md) var(--spacing-md);
    }
}

@media (max-width: 480px) {
    .window-controls {
        display: none;
    }

    .app-title {
        font-size: var(--font-size-md);
    }

    .toolbar-left {
        flex: 1 1 100%;
        order: 1;
    }

    .toolbar-right {
        order: 2;
    }

    .breadcrumb {
        flex-wrap: wrap;
    }

    .file-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    }

    .sidebar-section {
        margin-bottom: var(--spacing-md);
    }

    .cloud-drive-item {
        padding: var(--spacing-sm);
    }

    .drive-info {
        min-width: 0;
    }

    .drive-name {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

::-webkit-scrollbar-track {
    background: transparent;
}

::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* 选择状态 */
.file-item.selected {
    background: rgba(64, 123, 255, 0.15);
    border-color: rgba(64, 123, 255, 0.3);
}

.file-item.selected .file-name {
    color: var(--cd-primary);
}

/* 拖拽状态 */
.file-item.dragging {
    opacity: 0.5;
    transform: rotate(5deg);
}

.drop-zone {
    border: 2px dashed var(--cd-primary);
    background: rgba(64, 123, 255, 0.1);
}

/* 加载状态 */
.loading {
    position: relative;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.05);
    border-radius: inherit;
    animation: pulse 1.5s infinite;
}

/* 无障碍支持 */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
    .glass-panel, .glass-card, .glass-btn {
        border-width: 2px;
        border-color: rgba(255, 255, 255, 0.6);
    }

    .file-item:hover {
        border-color: var(--cd-primary);
    }
}