/* CloudDrive 2.0 玻璃拟态效果样式 */
:root {
    /* 玻璃拟态颜色 */
    --glass-bg: rgba(255, 255, 255, 0.08);
    --glass-bg-light: rgba(255, 255, 255, 0.12);
    --glass-bg-dark: rgba(0, 0, 0, 0.08);
    --glass-border: rgba(255, 255, 255, 0.15);
    --glass-border-strong: rgba(255, 255, 255, 0.25);
    --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    --glass-shadow-hover: 0 12px 40px rgba(0, 0, 0, 0.18);
    --glass-shadow-active: 0 4px 16px rgba(0, 0, 0, 0.15);
    
    /* 模糊效果 */
    --blur-light: blur(8px);
    --blur-medium: blur(16px);
    --blur-heavy: blur(24px);
    --blur-ultra: blur(32px);
    
    /* 液体玻璃颜色 */
    --liquid-primary: rgba(64, 123, 255, 0.25);
    --liquid-secondary: rgba(120, 119, 198, 0.25);
    --liquid-accent: rgba(255, 69, 58, 0.25);
    --liquid-success: rgba(52, 199, 89, 0.25);
    --liquid-warning: rgba(255, 204, 0, 0.25);
    
    /* 过渡动画 */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    --transition-spring: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

/* 基础玻璃面板 */
.glass-panel {
    background: var(--glass-bg);
    backdrop-filter: var(--blur-medium);
    -webkit-backdrop-filter: var(--blur-medium);
    border: 1px solid var(--glass-border);
    border-radius: 12px;
    box-shadow: var(--glass-shadow);
    transition: var(--transition-smooth);
    position: relative;
    overflow: hidden;
}

.glass-panel::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, 
        transparent 0%, 
        rgba(255, 255, 255, 0.3) 50%, 
        transparent 100%);
    z-index: 1;
}

.glass-panel:hover {
    background: var(--glass-bg-light);
    border-color: var(--glass-border-strong);
    box-shadow: var(--glass-shadow-hover);
    transform: translateY(-1px);
}

/* 玻璃卡片 */
.glass-card {
    background: var(--glass-bg);
    backdrop-filter: var(--blur-light);
    -webkit-backdrop-filter: var(--blur-light);
    border: 1px solid var(--glass-border);
    border-radius: 10px;
    box-shadow: var(--glass-shadow);
    transition: var(--transition-smooth);
    position: relative;
    overflow: hidden;
}

.glass-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, 
        rgba(255, 255, 255, 0.08) 0%, 
        transparent 50%, 
        rgba(255, 255, 255, 0.04) 100%);
    pointer-events: none;
    z-index: 1;
}

.glass-card:hover {
    background: var(--glass-bg-light);
    box-shadow: var(--glass-shadow-hover);
    transform: translateY(-2px) scale(1.01);
    border-color: var(--glass-border-strong);
}

.glass-card:active {
    transform: translateY(0) scale(0.99);
    box-shadow: var(--glass-shadow-active);
}

/* 玻璃按钮 */
.glass-btn {
    background: var(--glass-bg);
    backdrop-filter: var(--blur-light);
    -webkit-backdrop-filter: var(--blur-light);
    border: 1px solid var(--glass-border);
    border-radius: 8px;
    color: rgba(255, 255, 255, 0.9);
    padding: 8px 16px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-smooth);
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 14px;
}

.glass-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, 
        transparent 0%, 
        rgba(255, 255, 255, 0.15) 50%, 
        transparent 100%);
    transition: left 0.6s ease;
    z-index: 1;
}

.glass-btn:hover::before {
    left: 100%;
}

.glass-btn:hover {
    background: var(--glass-bg-light);
    box-shadow: var(--glass-shadow);
    transform: translateY(-1px);
    border-color: var(--glass-border-strong);
}

.glass-btn:active {
    transform: translateY(0) scale(0.98);
}

.glass-btn.primary {
    background: linear-gradient(135deg, 
        var(--liquid-primary) 0%, 
        var(--liquid-secondary) 100%);
    border: 1px solid rgba(64, 123, 255, 0.3);
    color: rgba(255, 255, 255, 0.95);
}

.glass-btn.primary:hover {
    background: linear-gradient(135deg, 
        rgba(64, 123, 255, 0.35) 0%, 
        rgba(120, 119, 198, 0.35) 100%);
}

.glass-btn.success {
    background: linear-gradient(135deg, 
        var(--liquid-success) 0%, 
        rgba(52, 199, 89, 0.3) 100%);
    border: 1px solid rgba(52, 199, 89, 0.3);
}

.glass-btn.warning {
    background: linear-gradient(135deg, 
        var(--liquid-warning) 0%, 
        rgba(255, 204, 0, 0.3) 100%);
    border: 1px solid rgba(255, 204, 0, 0.3);
}

.glass-btn.danger {
    background: linear-gradient(135deg, 
        var(--liquid-accent) 0%, 
        rgba(255, 69, 58, 0.3) 100%);
    border: 1px solid rgba(255, 69, 58, 0.3);
}

/* 玻璃输入框 */
.glass-input {
    background: var(--glass-bg);
    backdrop-filter: var(--blur-light);
    -webkit-backdrop-filter: var(--blur-light);
    border: 1px solid var(--glass-border);
    border-radius: 8px;
    padding: 8px 12px;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: var(--transition-smooth);
    position: relative;
}

.glass-input:focus-within {
    background: var(--glass-bg-light);
    border-color: rgba(64, 123, 255, 0.4);
    box-shadow: 0 0 0 3px rgba(64, 123, 255, 0.1);
}

.glass-input input {
    background: transparent;
    border: none;
    outline: none;
    color: rgba(255, 255, 255, 0.9);
    flex: 1;
    font-size: 14px;
    font-family: inherit;
}

.glass-input input::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.glass-input i {
    color: rgba(255, 255, 255, 0.6);
    font-size: 14px;
}

/* 液体覆盖层 */
.liquid-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
    opacity: 0.7;
}

.liquid-blob {
    position: absolute;
    border-radius: 50%;
    filter: blur(60px);
    animation: liquidFloat 25s infinite ease-in-out;
    mix-blend-mode: multiply;
}

.liquid-blob:nth-child(1) {
    width: 400px;
    height: 400px;
    background: var(--liquid-primary);
    top: 15%;
    left: 10%;
    animation-delay: 0s;
}

.liquid-blob:nth-child(2) {
    width: 300px;
    height: 300px;
    background: var(--liquid-secondary);
    top: 60%;
    right: 15%;
    animation-delay: -8s;
}

.liquid-blob:nth-child(3) {
    width: 350px;
    height: 350px;
    background: var(--liquid-success);
    bottom: 20%;
    left: 45%;
    animation-delay: -16s;
}

.liquid-blob:nth-child(4) {
    width: 250px;
    height: 250px;
    background: var(--liquid-warning);
    top: 30%;
    right: 40%;
    animation-delay: -12s;
}

@keyframes liquidFloat {
    0%, 100% {
        transform: translate(0, 0) scale(1) rotate(0deg);
    }
    25% {
        transform: translate(40px, -60px) scale(1.1) rotate(90deg);
    }
    50% {
        transform: translate(-30px, 30px) scale(0.9) rotate(180deg);
    }
    75% {
        transform: translate(60px, 15px) scale(1.05) rotate(270deg);
    }
}

/* 闪烁效果 */
.shimmer {
    position: relative;
    overflow: hidden;
}

.shimmer::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, 
        transparent 0%, 
        rgba(255, 255, 255, 0.3) 50%, 
        transparent 100%);
    animation: shimmer 2.5s infinite;
}

@keyframes shimmer {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* 脉冲效果 */
.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(64, 123, 255, 0.4);
    }
    70% {
        box-shadow: 0 0 0 12px rgba(64, 123, 255, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(64, 123, 255, 0);
    }
}

/* 发光效果 */
.glow {
    position: relative;
}

.glow::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, 
        var(--liquid-primary), 
        var(--liquid-secondary), 
        var(--liquid-success));
    border-radius: inherit;
    z-index: -1;
    filter: blur(10px);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.glow:hover::before {
    opacity: 0.8;
}

/* 磨砂玻璃变体 */
.frosted-glass {
    background: rgba(255, 255, 255, 0.04);
    backdrop-filter: blur(30px) saturate(200%);
    -webkit-backdrop-filter: blur(30px) saturate(200%);
    border: 1px solid rgba(255, 255, 255, 0.08);
}

/* 液体按钮 */
.liquid-btn {
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, 
        var(--liquid-primary) 0%, 
        var(--liquid-secondary) 100%);
    border: none;
    border-radius: 10px;
    color: white;
    padding: 12px 24px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-smooth);
}

.liquid-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.25);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.8s ease, height 0.8s ease;
}

.liquid-btn:hover::before {
    width: 400px;
    height: 400px;
}

.liquid-btn:active {
    transform: scale(0.96);
}

/* 旋转动画 */
.spinning {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* 响应式玻璃效果 */
@media (max-width: 768px) {
    .glass-panel, .glass-card {
        backdrop-filter: var(--blur-light);
        -webkit-backdrop-filter: var(--blur-light);
    }
    
    .liquid-blob {
        filter: blur(30px);
    }
}

/* 减少动画 */
@media (prefers-reduced-motion: reduce) {
    .glass-panel, .glass-card, .glass-btn {
        transition: none;
    }
    
    .liquid-blob {
        animation: none;
    }
    
    .shimmer::after {
        animation: none;
    }
    
    .spinning {
        animation: none;
    }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
    .glass-panel, .glass-card, .glass-btn {
        border-width: 2px;
        border-color: rgba(255, 255, 255, 0.5);
    }
}

/* 暗色主题优化 */
@media (prefers-color-scheme: dark) {
    :root {
        --glass-bg: rgba(255, 255, 255, 0.06);
        --glass-bg-light: rgba(255, 255, 255, 0.1);
        --glass-border: rgba(255, 255, 255, 0.12);
    }
}
