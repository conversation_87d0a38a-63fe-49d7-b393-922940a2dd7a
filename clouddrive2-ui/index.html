<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CloudDrive 2.0 - 云盘管理器</title>
    <link rel="stylesheet" href="styles/glass-morphism.css">
    <link rel="stylesheet" href="styles/clouddrive-theme.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <!-- 动态背景 -->
    <div class="background-container">
        <div class="gradient-bg"></div>
        <div class="liquid-overlay"></div>
    </div>

    <!-- 主应用容器 -->
    <div class="app-container">
        <!-- 顶部标题栏 -->
        <header class="title-bar glass-panel">
            <div class="title-bar-left">
                <div class="window-controls">
                    <span class="control-btn close"></span>
                    <span class="control-btn minimize"></span>
                    <span class="control-btn maximize"></span>
                </div>
                <div class="app-title">
                    <i class="fas fa-cloud"></i>
                    <span>CloudDrive</span>
                    <span class="version">v2.3.1</span>
                </div>
            </div>
            <div class="title-bar-center">
                <div class="search-container glass-input">
                    <i class="fas fa-search"></i>
                    <input type="text" placeholder="搜索文件和文件夹..." id="global-search">
                    <button class="search-filter-btn" title="搜索筛选">
                        <i class="fas fa-filter"></i>
                    </button>
                </div>
            </div>
            <div class="title-bar-right">
                <button class="toolbar-btn glass-btn" title="同步状态">
                    <i class="fas fa-sync-alt"></i>
                </button>
                <button class="toolbar-btn glass-btn" title="通知">
                    <i class="fas fa-bell"></i>
                    <span class="notification-badge">3</span>
                </button>
                <button class="toolbar-btn glass-btn" title="设置">
                    <i class="fas fa-cog"></i>
                </button>
                <div class="user-menu">
                    <img src="https://via.placeholder.com/32x32/007AFF/FFFFFF?text=用" alt="用户头像" class="user-avatar">
                </div>
            </div>
        </header>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 左侧边栏 -->
            <aside class="sidebar glass-panel">
                <!-- 快速访问 -->
                <div class="sidebar-section">
                    <h3>快速访问</h3>
                    <div class="quick-access-list">
                        <div class="quick-access-item active">
                            <i class="fas fa-home"></i>
                            <span>主页</span>
                        </div>
                        <div class="quick-access-item">
                            <i class="fas fa-star"></i>
                            <span>收藏夹</span>
                        </div>
                        <div class="quick-access-item">
                            <i class="fas fa-clock"></i>
                            <span>最近访问</span>
                        </div>
                        <div class="quick-access-item">
                            <i class="fas fa-trash"></i>
                            <span>回收站</span>
                        </div>
                    </div>
                </div>

                <!-- 云盘列表 -->
                <div class="sidebar-section">
                    <h3>我的云盘</h3>
                    <div class="cloud-drives-list">
                        <div class="cloud-drive-item">
                            <div class="drive-icon">
                                <i class="fab fa-google-drive" style="color: #4285f4;"></i>
                            </div>
                            <div class="drive-info">
                                <span class="drive-name">Google Drive</span>
                                <span class="drive-usage">15GB / 100GB</span>
                            </div>
                            <div class="drive-status online"></div>
                        </div>
                        
                        <div class="cloud-drive-item">
                            <div class="drive-icon">
                                <i class="fab fa-dropbox" style="color: #0061ff;"></i>
                            </div>
                            <div class="drive-info">
                                <span class="drive-name">Dropbox</span>
                                <span class="drive-usage">8.5GB / 16GB</span>
                            </div>
                            <div class="drive-status online"></div>
                        </div>
                        
                        <div class="cloud-drive-item">
                            <div class="drive-icon">
                                <i class="fab fa-microsoft" style="color: #00a1f1;"></i>
                            </div>
                            <div class="drive-info">
                                <span class="drive-name">OneDrive</span>
                                <span class="drive-usage">12GB / 1TB</span>
                            </div>
                            <div class="drive-status syncing"></div>
                        </div>
                        
                        <div class="cloud-drive-item">
                            <div class="drive-icon">
                                <i class="fas fa-cloud" style="color: #ff6b35;"></i>
                            </div>
                            <div class="drive-info">
                                <span class="drive-name">阿里云盘</span>
                                <span class="drive-usage">45GB / 无限</span>
                            </div>
                            <div class="drive-status online"></div>
                        </div>
                        
                        <div class="cloud-drive-item">
                            <div class="drive-icon">
                                <i class="fas fa-cloud" style="color: #2196f3;"></i>
                            </div>
                            <div class="drive-info">
                                <span class="drive-name">百度网盘</span>
                                <span class="drive-usage">156GB / 2TB</span>
                            </div>
                            <div class="drive-status offline"></div>
                        </div>
                    </div>
                    
                    <button class="add-drive-btn glass-btn">
                        <i class="fas fa-plus"></i>
                        <span>添加云盘</span>
                    </button>
                </div>

                <!-- 存储统计 -->
                <div class="sidebar-section">
                    <h3>存储统计</h3>
                    <div class="storage-stats glass-card">
                        <div class="total-storage">
                            <span class="storage-label">总存储空间</span>
                            <span class="storage-value">3.2TB</span>
                        </div>
                        <div class="storage-breakdown">
                            <div class="storage-item">
                                <div class="storage-color" style="background: #4285f4;"></div>
                                <span>文档</span>
                                <span>45GB</span>
                            </div>
                            <div class="storage-item">
                                <div class="storage-color" style="background: #ea4335;"></div>
                                <span>图片</span>
                                <span>128GB</span>
                            </div>
                            <div class="storage-item">
                                <div class="storage-color" style="background: #fbbc04;"></div>
                                <span>视频</span>
                                <span>892GB</span>
                            </div>
                            <div class="storage-item">
                                <div class="storage-color" style="background: #34a853;"></div>
                                <span>其他</span>
                                <span>67GB</span>
                            </div>
                        </div>
                    </div>
                </div>
            </aside>

            <!-- 主文件区域 -->
            <section class="file-area">
                <!-- 工具栏 -->
                <div class="toolbar glass-panel">
                    <div class="toolbar-left">
                        <div class="breadcrumb">
                            <span class="breadcrumb-item">
                                <i class="fas fa-home"></i>
                                <span>主页</span>
                            </span>
                            <i class="fas fa-chevron-right breadcrumb-separator"></i>
                            <span class="breadcrumb-item">
                                <span>文档</span>
                            </span>
                            <i class="fas fa-chevron-right breadcrumb-separator"></i>
                            <span class="breadcrumb-item active">
                                <span>工作文件</span>
                            </span>
                        </div>
                    </div>
                    
                    <div class="toolbar-center">
                        <div class="view-controls">
                            <button class="view-btn glass-btn active" data-view="grid" title="网格视图">
                                <i class="fas fa-th"></i>
                            </button>
                            <button class="view-btn glass-btn" data-view="list" title="列表视图">
                                <i class="fas fa-list"></i>
                            </button>
                            <button class="view-btn glass-btn" data-view="detail" title="详细视图">
                                <i class="fas fa-th-list"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="toolbar-right">
                        <button class="action-btn glass-btn" title="新建文件夹">
                            <i class="fas fa-folder-plus"></i>
                        </button>
                        <button class="action-btn glass-btn" title="上传文件">
                            <i class="fas fa-upload"></i>
                        </button>
                        <button class="action-btn glass-btn" title="更多操作">
                            <i class="fas fa-ellipsis-h"></i>
                        </button>
                    </div>
                </div>

                <!-- 文件列表 -->
                <div class="file-list-container">
                    <div class="file-grid" id="file-grid">
                        <!-- 文件夹 -->
                        <div class="file-item folder glass-card" data-type="folder">
                            <div class="file-icon">
                                <i class="fas fa-folder" style="color: #ffa726;"></i>
                            </div>
                            <div class="file-info">
                                <span class="file-name">图片收藏</span>
                                <span class="file-meta">23个文件</span>
                            </div>
                            <div class="file-actions">
                                <button class="file-action-btn" title="更多">
                                    <i class="fas fa-ellipsis-h"></i>
                                </button>
                            </div>
                        </div>

                        <div class="file-item folder glass-card" data-type="folder">
                            <div class="file-icon">
                                <i class="fas fa-folder" style="color: #66bb6a;"></i>
                            </div>
                            <div class="file-info">
                                <span class="file-name">工作文档</span>
                                <span class="file-meta">156个文件</span>
                            </div>
                            <div class="file-actions">
                                <button class="file-action-btn" title="更多">
                                    <i class="fas fa-ellipsis-h"></i>
                                </button>
                            </div>
                        </div>

                        <!-- 文件 -->
                        <div class="file-item file glass-card" data-type="file">
                            <div class="file-icon">
                                <i class="fas fa-file-pdf" style="color: #f44336;"></i>
                            </div>
                            <div class="file-info">
                                <span class="file-name">项目报告.pdf</span>
                                <span class="file-meta">2.3MB • 2小时前</span>
                            </div>
                            <div class="file-actions">
                                <button class="file-action-btn" title="更多">
                                    <i class="fas fa-ellipsis-h"></i>
                                </button>
                            </div>
                        </div>

                        <div class="file-item file glass-card" data-type="file">
                            <div class="file-icon">
                                <i class="fas fa-file-word" style="color: #2196f3;"></i>
                            </div>
                            <div class="file-info">
                                <span class="file-name">会议纪要.docx</span>
                                <span class="file-meta">1.8MB • 昨天</span>
                            </div>
                            <div class="file-actions">
                                <button class="file-action-btn" title="更多">
                                    <i class="fas fa-ellipsis-h"></i>
                                </button>
                            </div>
                        </div>

                        <div class="file-item file glass-card" data-type="file">
                            <div class="file-icon">
                                <i class="fas fa-file-excel" style="color: #4caf50;"></i>
                            </div>
                            <div class="file-info">
                                <span class="file-name">数据分析.xlsx</span>
                                <span class="file-meta">5.2MB • 3天前</span>
                            </div>
                            <div class="file-actions">
                                <button class="file-action-btn" title="更多">
                                    <i class="fas fa-ellipsis-h"></i>
                                </button>
                            </div>
                        </div>

                        <div class="file-item file glass-card" data-type="file">
                            <div class="file-icon">
                                <i class="fas fa-file-image" style="color: #ff9800;"></i>
                            </div>
                            <div class="file-info">
                                <span class="file-name">设计稿.png</span>
                                <span class="file-meta">8.7MB • 1周前</span>
                            </div>
                            <div class="file-actions">
                                <button class="file-action-btn" title="更多">
                                    <i class="fas fa-ellipsis-h"></i>
                                </button>
                            </div>
                        </div>

                        <div class="file-item file glass-card" data-type="file">
                            <div class="file-icon">
                                <i class="fas fa-file-video" style="color: #e91e63;"></i>
                            </div>
                            <div class="file-info">
                                <span class="file-name">演示视频.mp4</span>
                                <span class="file-meta">156MB • 2周前</span>
                            </div>
                            <div class="file-actions">
                                <button class="file-action-btn" title="更多">
                                    <i class="fas fa-ellipsis-h"></i>
                                </button>
                            </div>
                        </div>

                        <div class="file-item file glass-card" data-type="file">
                            <div class="file-icon">
                                <i class="fas fa-file-archive" style="color: #795548;"></i>
                            </div>
                            <div class="file-info">
                                <span class="file-name">备份文件.zip</span>
                                <span class="file-meta">45MB • 1个月前</span>
                            </div>
                            <div class="file-actions">
                                <button class="file-action-btn" title="更多">
                                    <i class="fas fa-ellipsis-h"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 右侧详情面板 -->
            <aside class="details-panel glass-panel">
                <div class="panel-header">
                    <h3>文件详情</h3>
                    <button class="panel-close-btn">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <div class="file-preview">
                    <div class="preview-placeholder">
                        <i class="fas fa-file"></i>
                        <p>选择文件查看详情</p>
                    </div>
                </div>
                
                <div class="file-properties">
                    <div class="property-group">
                        <h4>基本信息</h4>
                        <div class="property-item">
                            <span class="property-label">名称</span>
                            <span class="property-value">-</span>
                        </div>
                        <div class="property-item">
                            <span class="property-label">大小</span>
                            <span class="property-value">-</span>
                        </div>
                        <div class="property-item">
                            <span class="property-label">修改时间</span>
                            <span class="property-value">-</span>
                        </div>
                        <div class="property-item">
                            <span class="property-label">位置</span>
                            <span class="property-value">-</span>
                        </div>
                    </div>
                    
                    <div class="property-group">
                        <h4>共享设置</h4>
                        <div class="sharing-controls">
                            <button class="share-btn glass-btn">
                                <i class="fas fa-share-alt"></i>
                                <span>分享文件</span>
                            </button>
                            <button class="link-btn glass-btn">
                                <i class="fas fa-link"></i>
                                <span>复制链接</span>
                            </button>
                        </div>
                    </div>
                </div>
            </aside>
        </main>

        <!-- 状态栏 -->
        <footer class="status-bar glass-panel">
            <div class="status-left">
                <span class="status-item">
                    <i class="fas fa-folder"></i>
                    <span>8个项目</span>
                </span>
                <span class="status-item">
                    <i class="fas fa-hdd"></i>
                    <span>已用 1.2TB / 3.2TB</span>
                </span>
            </div>
            
            <div class="status-center">
                <div class="sync-status">
                    <i class="fas fa-sync-alt spinning"></i>
                    <span>正在同步...</span>
                </div>
            </div>
            
            <div class="status-right">
                <span class="status-item">
                    <i class="fas fa-wifi"></i>
                    <span>已连接</span>
                </span>
                <span class="status-item">
                    <i class="fas fa-clock"></i>
                    <span id="current-time">14:25</span>
                </span>
            </div>
        </footer>
    </div>

    <!-- 上下文菜单 -->
    <div class="context-menu glass-panel" id="context-menu">
        <div class="menu-item">
            <i class="fas fa-eye"></i>
            <span>预览</span>
        </div>
        <div class="menu-item">
            <i class="fas fa-download"></i>
            <span>下载</span>
        </div>
        <div class="menu-item">
            <i class="fas fa-edit"></i>
            <span>重命名</span>
        </div>
        <div class="menu-item">
            <i class="fas fa-copy"></i>
            <span>复制</span>
        </div>
        <div class="menu-item">
            <i class="fas fa-cut"></i>
            <span>剪切</span>
        </div>
        <div class="menu-separator"></div>
        <div class="menu-item">
            <i class="fas fa-share-alt"></i>
            <span>分享</span>
        </div>
        <div class="menu-item">
            <i class="fas fa-star"></i>
            <span>收藏</span>
        </div>
        <div class="menu-separator"></div>
        <div class="menu-item danger">
            <i class="fas fa-trash"></i>
            <span>删除</span>
        </div>
    </div>

    <!-- 模态框 -->
    <div class="modal-overlay" id="modal-overlay">
        <div class="modal glass-panel">
            <div class="modal-header">
                <h3 id="modal-title">标题</h3>
                <button class="modal-close" id="modal-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-content" id="modal-content">
                <!-- 模态框内容 -->
            </div>
        </div>
    </div>

    <!-- 脚本 -->
    <script src="js/liquid-effects.js"></script>
    <script src="js/clouddrive-app.js"></script>
</body>
</html>
