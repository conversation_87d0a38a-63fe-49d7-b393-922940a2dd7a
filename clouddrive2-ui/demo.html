<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CloudDrive 2.0 演示 - 优化版云盘管理界面</title>
    <link rel="stylesheet" href="styles/glass-morphism.css">
    <link rel="stylesheet" href="styles/clouddrive-theme.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .demo-header {
            text-align: center;
            padding: 3rem 2rem;
            margin-bottom: 2rem;
        }
        
        .demo-title {
            font-size: 3.5rem;
            font-weight: 700;
            background: linear-gradient(135deg, #407BFF 0%, #7877C6 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1rem;
            line-height: 1.2;
        }
        
        .demo-subtitle {
            font-size: 1.3rem;
            color: var(--cd-text-secondary);
            margin-bottom: 2rem;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
            line-height: 1.5;
        }
        
        .feature-showcase {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin: 3rem 0;
        }
        
        .feature-card {
            padding: 2.5rem;
            text-align: center;
            position: relative;
        }
        
        .feature-icon {
            font-size: 3.5rem;
            margin-bottom: 1.5rem;
            background: linear-gradient(135deg, var(--cd-primary) 0%, var(--cd-secondary) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .feature-title {
            font-size: 1.6rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: var(--cd-text-primary);
        }
        
        .feature-description {
            color: var(--cd-text-secondary);
            line-height: 1.6;
            font-size: 1rem;
        }
        
        .demo-actions {
            display: flex;
            justify-content: center;
            gap: 1.5rem;
            margin: 3rem 0;
            flex-wrap: wrap;
        }
        
        .demo-btn {
            padding: 1.2rem 2.5rem;
            font-size: 1.1rem;
            font-weight: 600;
            text-decoration: none;
            border-radius: 12px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.8rem;
            border: none;
            cursor: pointer;
        }
        
        .demo-btn.primary {
            background: linear-gradient(135deg, var(--cd-primary) 0%, var(--cd-secondary) 100%);
            color: white;
        }
        
        .demo-btn.secondary {
            background: rgba(255, 255, 255, 0.08);
            color: var(--cd-text-primary);
            border: 1px solid rgba(255, 255, 255, 0.15);
        }
        
        .demo-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
        }
        
        .tech-stack {
            margin: 4rem 0;
            text-align: center;
        }
        
        .tech-stack h3 {
            font-size: 1.8rem;
            margin-bottom: 1.5rem;
            color: var(--cd-text-primary);
        }
        
        .tech-badges {
            display: flex;
            justify-content: center;
            gap: 1rem;
            flex-wrap: wrap;
            max-width: 800px;
            margin: 0 auto;
        }
        
        .tech-badge {
            padding: 0.6rem 1.2rem;
            background: rgba(255, 255, 255, 0.08);
            border-radius: 25px;
            font-size: 0.9rem;
            color: var(--cd-text-secondary);
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }
        
        .tech-badge:hover {
            background: rgba(255, 255, 255, 0.12);
            color: var(--cd-text-primary);
            transform: translateY(-2px);
        }
        
        .comparison-section {
            margin: 4rem 0;
            text-align: center;
        }
        
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin: 2rem 0;
        }
        
        .comparison-item {
            padding: 2rem;
            border-radius: 16px;
        }
        
        .comparison-item.before {
            background: rgba(255, 69, 58, 0.1);
            border: 1px solid rgba(255, 69, 58, 0.2);
        }
        
        .comparison-item.after {
            background: rgba(52, 199, 89, 0.1);
            border: 1px solid rgba(52, 199, 89, 0.2);
        }
        
        .comparison-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }
        
        .comparison-item.before .comparison-title {
            color: var(--cd-error);
        }
        
        .comparison-item.after .comparison-title {
            color: var(--cd-success);
        }
        
        .comparison-list {
            list-style: none;
            text-align: left;
        }
        
        .comparison-list li {
            padding: 0.5rem 0;
            color: var(--cd-text-secondary);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .comparison-list li i {
            width: 16px;
        }
        
        @media (max-width: 768px) {
            .demo-title {
                font-size: 2.5rem;
            }
            
            .demo-actions {
                flex-direction: column;
                align-items: center;
            }
            
            .demo-btn {
                width: 100%;
                max-width: 300px;
                justify-content: center;
            }
            
            .comparison-grid {
                grid-template-columns: 1fr;
            }
            
            .feature-showcase {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- 动态背景 -->
    <div class="background-container">
        <div class="gradient-bg"></div>
        <div class="liquid-overlay"></div>
    </div>

    <!-- 演示内容 -->
    <div class="app-container">
        <!-- 演示头部 -->
        <header class="demo-header">
            <h1 class="demo-title">CloudDrive 2.0</h1>
            <p class="demo-subtitle">
                基于macOS设计理念的现代化云盘管理界面，融合玻璃拟态效果与液体动画，为您带来前所未有的视觉体验
            </p>
            
            <div class="demo-actions">
                <a href="index.html" class="demo-btn primary liquid-btn">
                    <i class="fas fa-rocket"></i>
                    体验完整界面
                </a>
                <a href="#features" class="demo-btn secondary glass-btn">
                    <i class="fas fa-info-circle"></i>
                    了解更多特性
                </a>
            </div>
        </header>

        <!-- 特性展示 -->
        <section id="features" class="glass-panel" style="padding: 3rem; margin: 2rem 0;">
            <h2 style="text-align: center; font-size: 2.5rem; margin-bottom: 2rem; color: var(--cd-text-primary);">
                核心特性
            </h2>
            
            <div class="feature-showcase">
                <div class="feature-card glass-card">
                    <div class="feature-icon">
                        <i class="fas fa-gem"></i>
                    </div>
                    <h3 class="feature-title">玻璃拟态设计</h3>
                    <p class="feature-description">
                        采用最新的玻璃拟态设计语言，通过半透明材质、高斯模糊和精致的光影效果，创造出富有层次感的视觉体验。
                    </p>
                </div>
                
                <div class="feature-card glass-card">
                    <div class="feature-icon">
                        <i class="fas fa-water"></i>
                    </div>
                    <h3 class="feature-title">液体动画效果</h3>
                    <p class="feature-description">
                        灵感来源于iOS 16的液体玻璃特性，动态模糊、半透明度和颜色扩散营造出"流动中的液体"般的视觉效果。
                    </p>
                </div>
                
                <div class="feature-card glass-card">
                    <div class="feature-icon">
                        <i class="fab fa-apple"></i>
                    </div>
                    <h3 class="feature-title">macOS风格设计</h3>
                    <p class="feature-description">
                        遵循苹果设计规范，采用直观的布局逻辑、优雅的交互动画和精致的视觉细节，提供熟悉而高效的操作体验。
                    </p>
                </div>
                
                <div class="feature-card glass-card">
                    <div class="feature-icon">
                        <i class="fas fa-cloud"></i>
                    </div>
                    <h3 class="feature-title">多云盘统一管理</h3>
                    <p class="feature-description">
                        支持Google Drive、OneDrive、Dropbox、阿里云盘、百度网盘等主流云存储服务的统一管理和文件操作。
                    </p>
                </div>
                
                <div class="feature-card glass-card">
                    <div class="feature-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <h3 class="feature-title">响应式适配</h3>
                    <p class="feature-description">
                        完美适配桌面、平板和移动设备，采用自适应布局和触控优化，确保在任何设备上都能获得最佳体验。
                    </p>
                </div>
                
                <div class="feature-card glass-card">
                    <div class="feature-icon">
                        <i class="fas fa-bolt"></i>
                    </div>
                    <h3 class="feature-title">性能优化</h3>
                    <p class="feature-description">
                        采用硬件加速动画、智能性能检测和资源优化策略，确保在低端设备上也能流畅运行。
                    </p>
                </div>
            </div>
        </section>

        <!-- 对比展示 -->
        <section class="comparison-section glass-panel" style="padding: 3rem; margin: 2rem 0;">
            <h2 style="font-size: 2.5rem; margin-bottom: 2rem; color: var(--cd-text-primary);">
                设计对比
            </h2>
            
            <div class="comparison-grid">
                <div class="comparison-item before">
                    <h3 class="comparison-title">
                        <i class="fas fa-times"></i>
                        传统界面
                    </h3>
                    <ul class="comparison-list">
                        <li><i class="fas fa-minus"></i>平面化设计缺乏层次感</li>
                        <li><i class="fas fa-minus"></i>静态界面缺少动态反馈</li>
                        <li><i class="fas fa-minus"></i>功能分散操作复杂</li>
                        <li><i class="fas fa-minus"></i>视觉效果单调乏味</li>
                        <li><i class="fas fa-minus"></i>移动端体验不佳</li>
                    </ul>
                </div>
                
                <div class="comparison-item after">
                    <h3 class="comparison-title">
                        <i class="fas fa-check"></i>
                        CloudDrive 2.0
                    </h3>
                    <ul class="comparison-list">
                        <li><i class="fas fa-plus"></i>玻璃拟态营造丰富层次</li>
                        <li><i class="fas fa-plus"></i>液体动画提供即时反馈</li>
                        <li><i class="fas fa-plus"></i>统一界面简化操作流程</li>
                        <li><i class="fas fa-plus"></i>视觉效果精美动人</li>
                        <li><i class="fas fa-plus"></i>全平台一致体验</li>
                    </ul>
                </div>
            </div>
        </section>

        <!-- 技术栈 -->
        <section class="tech-stack glass-panel" style="padding: 2.5rem; margin: 2rem 0;">
            <h3>技术实现</h3>
            <div class="tech-badges">
                <span class="tech-badge">HTML5 语义化</span>
                <span class="tech-badge">CSS3 玻璃拟态</span>
                <span class="tech-badge">JavaScript ES6+</span>
                <span class="tech-badge">CSS Grid 布局</span>
                <span class="tech-badge">Backdrop Filter</span>
                <span class="tech-badge">CSS 动画</span>
                <span class="tech-badge">响应式设计</span>
                <span class="tech-badge">性能优化</span>
                <span class="tech-badge">无障碍支持</span>
                <span class="tech-badge">中文本地化</span>
            </div>
        </section>

        <!-- 设计原则 -->
        <section class="glass-panel" style="padding: 3rem; margin: 2rem 0;">
            <h2 style="text-align: center; font-size: 2.5rem; margin-bottom: 2rem; color: var(--cd-text-primary);">
                设计原则
            </h2>
            
            <div class="feature-showcase">
                <div class="feature-card">
                    <h4 style="color: var(--cd-primary); margin-bottom: 1rem; font-size: 1.3rem;">视觉层次</h4>
                    <p style="color: var(--cd-text-secondary);">
                        通过不同程度的模糊效果和透明度变化，建立清晰的视觉层次结构，引导用户注意力。
                    </p>
                </div>
                
                <div class="feature-card">
                    <h4 style="color: var(--cd-secondary); margin-bottom: 1rem; font-size: 1.3rem;">交互反馈</h4>
                    <p style="color: var(--cd-text-secondary);">
                        每个交互都有相应的视觉反馈，包括悬停效果、点击动画和状态变化，提升操作确定性。
                    </p>
                </div>
                
                <div class="feature-card">
                    <h4 style="color: var(--cd-success); margin-bottom: 1rem; font-size: 1.3rem;">信息架构</h4>
                    <p style="color: var(--cd-text-secondary);">
                        遵循用户心理模型，将相关功能分组，减少认知负担，提高操作效率。
                    </p>
                </div>
            </div>
        </section>

        <!-- 行动号召 -->
        <section class="glass-panel" style="padding: 3rem; margin: 2rem 0; text-align: center;">
            <h2 style="font-size: 2.5rem; margin-bottom: 1rem; color: var(--cd-text-primary);">
                准备好体验未来的云盘管理了吗？
            </h2>
            <p style="font-size: 1.2rem; color: var(--cd-text-secondary); margin-bottom: 2rem;">
                立即启动应用，探索这个为现代云存储工作流程设计的美观、实用界面。
            </p>
            
            <div class="demo-actions">
                <a href="index.html" class="demo-btn primary liquid-btn">
                    <i class="fas fa-play"></i>
                    启动应用
                </a>
                <button class="demo-btn secondary glass-btn" onclick="window.history.back()">
                    <i class="fas fa-arrow-left"></i>
                    返回上页
                </button>
            </div>
        </section>

        <!-- 页脚 -->
        <footer style="text-align: center; padding: 2rem; color: var(--cd-text-tertiary);">
            <p>
                基于CloudDrive2.com界面优化设计，融合iOS 16液体玻璃特性
            </p>
            <p style="margin-top: 0.5rem;">
                使用现代Web技术精心打造 ❤️
            </p>
        </footer>
    </div>

    <!-- 脚本 -->
    <script src="js/liquid-effects.js"></script>
    <script>
        // 演示页面特定增强
        document.addEventListener('DOMContentLoaded', () => {
            // 添加额外的液体blob用于演示
            if (window.cloudDriveLiquidEffects) {
                const overlay = document.querySelector('.liquid-overlay');
                if (overlay) {
                    // 添加更多彩色blob用于演示
                    const demoColors = [
                        'rgba(255, 149, 0, 0.2)',
                        'rgba(255, 45, 85, 0.2)',
                        'rgba(88, 86, 214, 0.2)'
                    ];
                    
                    demoColors.forEach((color, i) => {
                        const blob = document.createElement('div');
                        blob.className = 'liquid-blob';
                        blob.style.cssText = `
                            position: absolute;
                            width: ${200 + Math.random() * 150}px;
                            height: ${200 + Math.random() * 150}px;
                            background: ${color};
                            border-radius: 50%;
                            filter: blur(50px);
                            animation: liquidFloat ${20 + Math.random() * 15}s infinite ease-in-out;
                            animation-delay: ${i * 4}s;
                            top: ${Math.random() * 100}%;
                            left: ${Math.random() * 100}%;
                        `;
                        overlay.appendChild(blob);
                    });
                }
            }
            
            // 平滑滚动
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });
            
            // 视差效果
            window.addEventListener('scroll', () => {
                const scrolled = window.pageYOffset;
                const title = document.querySelector('.demo-title');
                if (title) {
                    title.style.transform = `translateY(${scrolled * 0.2}px)`;
                }
            });
        });
    </script>
</body>
</html>
