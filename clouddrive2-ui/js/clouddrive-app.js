/**
 * CloudDrive 2.0 主应用程序
 * 云盘管理界面的核心功能实现
 */

class CloudDriveApp {
    constructor() {
        this.currentView = 'grid';
        this.selectedFiles = new Set();
        this.currentPath = ['主页'];
        this.files = [];
        this.cloudDrives = [];
        this.isLoading = false;
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadInitialData();
        this.setupKeyboardShortcuts();
        this.initializeTooltips();
        this.updateClock();
        this.setupContextMenu();
        
        console.log('CloudDrive 应用已初始化');
    }

    setupEventListeners() {
        // 视图切换
        document.querySelectorAll('.view-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const view = e.currentTarget.dataset.view;
                this.switchView(view);
            });
        });

        // 搜索功能
        const searchInput = document.getElementById('global-search');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.handleSearch(e.target.value);
            });
        }

        // 文件操作
        this.setupFileActions();
        
        // 工具栏按钮
        this.setupToolbarActions();
        
        // 云盘管理
        this.setupCloudDriveActions();
        
        // 模态框
        this.setupModalActions();
        
        // 拖拽上传
        this.setupDragAndDrop();
    }

    setupFileActions() {
        document.addEventListener('click', (e) => {
            const fileItem = e.target.closest('.file-item');
            if (!fileItem) return;

            const fileName = fileItem.querySelector('.file-name').textContent;
            
            if (e.target.closest('.file-action-btn')) {
                e.stopPropagation();
                this.showFileContextMenu(fileItem, e.target);
            } else {
                // 文件选择
                if (e.ctrlKey || e.metaKey) {
                    this.toggleFileSelection(fileItem);
                } else {
                    this.selectFile(fileItem);
                }
                
                // 双击打开
                if (e.detail === 2) {
                    this.openFile(fileName);
                }
            }
        });
    }

    setupToolbarActions() {
        // 刷新按钮
        document.querySelector('[title="同步状态"]')?.addEventListener('click', () => {
            this.refreshFiles();
        });
        
        // 新建文件夹
        document.querySelector('[title="新建文件夹"]')?.addEventListener('click', () => {
            this.createNewFolder();
        });
        
        // 上传文件
        document.querySelector('[title="上传文件"]')?.addEventListener('click', () => {
            this.uploadFiles();
        });
        
        // 面包屑导航
        document.addEventListener('click', (e) => {
            const breadcrumbItem = e.target.closest('.breadcrumb-item');
            if (breadcrumbItem && !breadcrumbItem.classList.contains('active')) {
                const index = Array.from(breadcrumbItem.parentNode.children)
                    .filter(el => el.classList.contains('breadcrumb-item'))
                    .indexOf(breadcrumbItem);
                this.navigateToPath(index);
            }
        });
    }

    setupCloudDriveActions() {
        // 云盘项点击
        document.addEventListener('click', (e) => {
            const driveItem = e.target.closest('.cloud-drive-item');
            if (driveItem) {
                this.selectCloudDrive(driveItem);
            }
        });
        
        // 添加云盘
        document.querySelector('.add-drive-btn')?.addEventListener('click', () => {
            this.showAddDriveModal();
        });
        
        // 快速访问
        document.addEventListener('click', (e) => {
            const quickItem = e.target.closest('.quick-access-item');
            if (quickItem) {
                this.handleQuickAccess(quickItem);
            }
        });
    }

    setupModalActions() {
        const modalOverlay = document.getElementById('modal-overlay');
        const modalClose = document.getElementById('modal-close');
        
        modalClose?.addEventListener('click', () => {
            this.closeModal();
        });
        
        modalOverlay?.addEventListener('click', (e) => {
            if (e.target === modalOverlay) {
                this.closeModal();
            }
        });
    }

    setupContextMenu() {
        const contextMenu = document.getElementById('context-menu');
        
        document.addEventListener('contextmenu', (e) => {
            const fileItem = e.target.closest('.file-item');
            if (fileItem) {
                e.preventDefault();
                this.showContextMenu(e.clientX, e.clientY, fileItem);
            }
        });
        
        document.addEventListener('click', () => {
            this.hideContextMenu();
        });
        
        // 上下文菜单项点击
        contextMenu?.addEventListener('click', (e) => {
            const menuItem = e.target.closest('.menu-item');
            if (menuItem) {
                this.handleContextMenuAction(menuItem);
            }
        });
    }

    setupDragAndDrop() {
        const fileArea = document.querySelector('.file-area');
        
        fileArea?.addEventListener('dragover', (e) => {
            e.preventDefault();
            fileArea.classList.add('drop-zone');
        });
        
        fileArea?.addEventListener('dragleave', (e) => {
            if (!fileArea.contains(e.relatedTarget)) {
                fileArea.classList.remove('drop-zone');
            }
        });
        
        fileArea?.addEventListener('drop', (e) => {
            e.preventDefault();
            fileArea.classList.remove('drop-zone');
            
            const files = Array.from(e.dataTransfer.files);
            this.handleFileUpload(files);
        });
    }

    switchView(viewType) {
        // 更新按钮状态
        document.querySelectorAll('.view-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-view="${viewType}"]`).classList.add('active');
        
        // 更新文件网格类
        const fileGrid = document.getElementById('file-grid');
        fileGrid.className = `file-${viewType}`;
        
        this.currentView = viewType;
        this.renderFiles();
    }

    handleSearch(query) {
        const filteredFiles = this.files.filter(file =>
            file.name.toLowerCase().includes(query.toLowerCase())
        );
        
        this.renderFiles(filteredFiles);
        
        // 高亮搜索结果
        if (query) {
            this.highlightSearchResults(query);
        }
    }

    selectFile(fileItem) {
        // 清除其他选择
        document.querySelectorAll('.file-item.selected').forEach(item => {
            item.classList.remove('selected');
        });
        
        fileItem.classList.add('selected');
        this.selectedFiles.clear();
        this.selectedFiles.add(fileItem.querySelector('.file-name').textContent);
        
        this.updateDetailsPanel(fileItem);
    }

    toggleFileSelection(fileItem) {
        const fileName = fileItem.querySelector('.file-name').textContent;
        
        if (fileItem.classList.contains('selected')) {
            fileItem.classList.remove('selected');
            this.selectedFiles.delete(fileName);
        } else {
            fileItem.classList.add('selected');
            this.selectedFiles.add(fileName);
        }
        
        this.updateSelectionInfo();
    }

    openFile(fileName) {
        const file = this.files.find(f => f.name === fileName);
        if (!file) return;
        
        if (file.type === 'folder') {
            this.navigateToFolder(fileName);
        } else {
            this.previewFile(file);
        }
    }

    navigateToFolder(folderName) {
        this.currentPath.push(folderName);
        this.updateBreadcrumb();
        this.loadFolderContents(folderName);
    }

    navigateToPath(index) {
        this.currentPath = this.currentPath.slice(0, index + 1);
        this.updateBreadcrumb();
        this.loadFolderContents(this.currentPath[this.currentPath.length - 1]);
    }

    updateBreadcrumb() {
        const breadcrumb = document.querySelector('.breadcrumb');
        if (!breadcrumb) return;
        
        breadcrumb.innerHTML = this.currentPath.map((path, index) => {
            const isLast = index === this.currentPath.length - 1;
            const icon = index === 0 ? '<i class="fas fa-home"></i>' : '';
            
            return `
                <span class="breadcrumb-item ${isLast ? 'active' : ''}">
                    ${icon}
                    <span>${path}</span>
                </span>
                ${!isLast ? '<i class="fas fa-chevron-right breadcrumb-separator"></i>' : ''}
            `;
        }).join('');
    }

    updateDetailsPanel(fileItem) {
        const fileName = fileItem.querySelector('.file-name').textContent;
        const fileMeta = fileItem.querySelector('.file-meta').textContent;
        
        // 更新属性信息
        const properties = document.querySelectorAll('.property-value');
        if (properties.length >= 4) {
            properties[0].textContent = fileName;
            properties[1].textContent = fileMeta.split(' • ')[0] || '-';
            properties[2].textContent = fileMeta.split(' • ')[1] || '-';
            properties[3].textContent = this.currentPath.join(' / ');
        }
        
        // 更新预览
        this.updateFilePreview(fileName);
    }

    updateFilePreview(fileName) {
        const preview = document.querySelector('.file-preview');
        const file = this.files.find(f => f.name === fileName);
        
        if (file && file.type === 'image') {
            preview.innerHTML = `<img src="${file.thumbnail}" alt="${fileName}" style="width: 100%; height: 100%; object-fit: cover; border-radius: 8px;">`;
        } else {
            preview.innerHTML = `
                <div class="preview-placeholder">
                    <i class="fas fa-file"></i>
                    <p>${fileName}</p>
                </div>
            `;
        }
    }

    showContextMenu(x, y, fileItem) {
        const contextMenu = document.getElementById('context-menu');
        if (!contextMenu) return;
        
        contextMenu.style.left = `${x}px`;
        contextMenu.style.top = `${y}px`;
        contextMenu.classList.add('show');
        
        // 存储当前文件项
        contextMenu.dataset.currentFile = fileItem.querySelector('.file-name').textContent;
    }

    hideContextMenu() {
        const contextMenu = document.getElementById('context-menu');
        contextMenu?.classList.remove('show');
    }

    handleContextMenuAction(menuItem) {
        const action = menuItem.textContent.trim();
        const fileName = document.getElementById('context-menu').dataset.currentFile;
        
        switch (action) {
            case '预览':
                this.previewFile(this.files.find(f => f.name === fileName));
                break;
            case '下载':
                this.downloadFile(fileName);
                break;
            case '重命名':
                this.renameFile(fileName);
                break;
            case '复制':
                this.copyFile(fileName);
                break;
            case '剪切':
                this.cutFile(fileName);
                break;
            case '分享':
                this.shareFile(fileName);
                break;
            case '收藏':
                this.favoriteFile(fileName);
                break;
            case '删除':
                this.deleteFile(fileName);
                break;
        }
        
        this.hideContextMenu();
    }

    async loadInitialData() {
        this.setLoading(true);
        
        try {
            // 模拟加载文件数据
            await this.delay(1000);
            
            this.files = [
                { name: '图片收藏', type: 'folder', size: '23个文件', modified: '2小时前', icon: 'fas fa-folder', color: '#ffa726' },
                { name: '工作文档', type: 'folder', size: '156个文件', modified: '昨天', icon: 'fas fa-folder', color: '#66bb6a' },
                { name: '项目报告.pdf', type: 'file', size: '2.3MB', modified: '2小时前', icon: 'fas fa-file-pdf', color: '#f44336' },
                { name: '会议纪要.docx', type: 'file', size: '1.8MB', modified: '昨天', icon: 'fas fa-file-word', color: '#2196f3' },
                { name: '数据分析.xlsx', type: 'file', size: '5.2MB', modified: '3天前', icon: 'fas fa-file-excel', color: '#4caf50' },
                { name: '设计稿.png', type: 'image', size: '8.7MB', modified: '1周前', icon: 'fas fa-file-image', color: '#ff9800', thumbnail: 'https://via.placeholder.com/200x200/ff9800/white?text=PNG' },
                { name: '演示视频.mp4', type: 'video', size: '156MB', modified: '2周前', icon: 'fas fa-file-video', color: '#e91e63' },
                { name: '备份文件.zip', type: 'archive', size: '45MB', modified: '1个月前', icon: 'fas fa-file-archive', color: '#795548' }
            ];
            
            this.renderFiles();
            this.updateStorageStats();
            
        } catch (error) {
            this.showNotification('加载数据失败', 'error');
        } finally {
            this.setLoading(false);
        }
    }

    renderFiles(filesToRender = this.files) {
        const fileGrid = document.getElementById('file-grid');
        if (!fileGrid) return;
        
        fileGrid.innerHTML = filesToRender.map(file => `
            <div class="file-item ${file.type} glass-card" data-type="${file.type}">
                <div class="file-icon">
                    <i class="${file.icon}" style="color: ${file.color};"></i>
                </div>
                <div class="file-info">
                    <span class="file-name">${file.name}</span>
                    <span class="file-meta">${file.size} • ${file.modified}</span>
                </div>
                <div class="file-actions">
                    <button class="file-action-btn" title="更多">
                        <i class="fas fa-ellipsis-h"></i>
                    </button>
                </div>
            </div>
        `).join('');
        
        // 添加玻璃效果
        setTimeout(() => {
            fileGrid.querySelectorAll('.file-item').forEach(item => {
                if (window.cloudDriveLiquidEffects) {
                    window.cloudDriveLiquidEffects.addGlassEffect(item);
                }
            });
        }, 100);
    }

    updateStorageStats() {
        // 更新存储统计信息
        const totalUsed = this.files.reduce((total, file) => {
            const size = parseFloat(file.size);
            const unit = file.size.match(/[A-Z]+/)?.[0];
            const multiplier = unit === 'GB' ? 1024 : unit === 'TB' ? 1024 * 1024 : 1;
            return total + (size * multiplier);
        }, 0);
        
        document.querySelector('.storage-value').textContent = `${(totalUsed / 1024).toFixed(1)}GB`;
    }

    updateClock() {
        const updateTime = () => {
            const now = new Date();
            const timeString = now.toLocaleTimeString('zh-CN', { 
                hour: '2-digit', 
                minute: '2-digit' 
            });
            
            const timeElement = document.getElementById('current-time');
            if (timeElement) {
                timeElement.textContent = timeString;
            }
        };
        
        updateTime();
        setInterval(updateTime, 60000); // 每分钟更新
    }

    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch (e.key) {
                    case 'f':
                        e.preventDefault();
                        document.getElementById('global-search')?.focus();
                        break;
                    case 'n':
                        e.preventDefault();
                        this.createNewFolder();
                        break;
                    case 'u':
                        e.preventDefault();
                        this.uploadFiles();
                        break;
                    case 'r':
                        e.preventDefault();
                        this.refreshFiles();
                        break;
                }
            }
            
            if (e.key === 'Escape') {
                this.closeModal();
                this.hideContextMenu();
            }
            
            if (e.key === 'Delete' && this.selectedFiles.size > 0) {
                this.deleteSelectedFiles();
            }
        });
    }

    initializeTooltips() {
        // 简单的工具提示实现
        document.querySelectorAll('[title]').forEach(element => {
            element.addEventListener('mouseenter', (e) => {
                const tooltip = document.createElement('div');
                tooltip.className = 'tooltip glass-panel';
                tooltip.textContent = e.target.title;
                tooltip.style.cssText = `
                    position: fixed;
                    z-index: 3000;
                    padding: 6px 10px;
                    font-size: 12px;
                    white-space: nowrap;
                    pointer-events: none;
                    border-radius: 6px;
                `;
                
                document.body.appendChild(tooltip);
                
                const rect = e.target.getBoundingClientRect();
                tooltip.style.left = `${rect.left + rect.width / 2 - tooltip.offsetWidth / 2}px`;
                tooltip.style.top = `${rect.top - tooltip.offsetHeight - 8}px`;
                
                e.target.addEventListener('mouseleave', () => {
                    tooltip.remove();
                }, { once: true });
            });
        });
    }

    // 工具方法
    setLoading(loading) {
        this.isLoading = loading;
        const syncIcon = document.querySelector('.sync-status i');
        if (syncIcon) {
            if (loading) {
                syncIcon.classList.add('spinning');
            } else {
                syncIcon.classList.remove('spinning');
            }
        }
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification glass-panel ${type}`;
        notification.innerHTML = `
            <i class="fas ${type === 'success' ? 'fa-check' : type === 'error' ? 'fa-exclamation-triangle' : 'fa-info'}"></i>
            <span>${message}</span>
        `;
        
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 4000;
            padding: 12px 16px;
            display: flex;
            align-items: center;
            gap: 8px;
            animation: slideInRight 0.3s ease;
            border-left: 3px solid ${type === 'success' ? 'var(--cd-success)' : type === 'error' ? 'var(--cd-error)' : 'var(--cd-primary)'};
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.style.animation = 'slideOutRight 0.3s ease';
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    }

    openModal(title, content) {
        const modal = document.getElementById('modal-overlay');
        const modalTitle = document.getElementById('modal-title');
        const modalContent = document.getElementById('modal-content');
        
        modalTitle.textContent = title;
        modalContent.innerHTML = content;
        modal.classList.add('active');
    }

    closeModal() {
        document.getElementById('modal-overlay')?.classList.remove('active');
    }

    // 占位方法 - 实际应用中需要实现
    refreshFiles() { this.showNotification('正在刷新文件列表...'); }
    createNewFolder() { this.showNotification('创建新文件夹功能'); }
    uploadFiles() { this.showNotification('上传文件功能'); }
    previewFile(file) { this.showNotification(`预览文件: ${file.name}`); }
    downloadFile(fileName) { this.showNotification(`下载文件: ${fileName}`); }
    renameFile(fileName) { this.showNotification(`重命名文件: ${fileName}`); }
    copyFile(fileName) { this.showNotification(`复制文件: ${fileName}`); }
    cutFile(fileName) { this.showNotification(`剪切文件: ${fileName}`); }
    shareFile(fileName) { this.showNotification(`分享文件: ${fileName}`); }
    favoriteFile(fileName) { this.showNotification(`收藏文件: ${fileName}`); }
    deleteFile(fileName) { this.showNotification(`删除文件: ${fileName}`, 'error'); }
    deleteSelectedFiles() { this.showNotification(`删除选中的 ${this.selectedFiles.size} 个文件`, 'error'); }
    handleFileUpload(files) { this.showNotification(`上传 ${files.length} 个文件`); }
    loadFolderContents(folderName) { this.showNotification(`加载文件夹: ${folderName}`); }
    selectCloudDrive(driveItem) { this.showNotification(`选择云盘: ${driveItem.querySelector('.drive-name').textContent}`); }
    showAddDriveModal() { this.openModal('添加云盘', '<p>添加云盘功能开发中...</p>'); }
    handleQuickAccess(item) { this.showNotification(`快速访问: ${item.textContent.trim()}`); }
    highlightSearchResults(query) { /* 实现搜索高亮 */ }
    updateSelectionInfo() { /* 更新选择信息 */ }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    window.cloudDriveApp = new CloudDriveApp();
});
