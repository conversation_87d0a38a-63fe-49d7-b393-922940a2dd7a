/**
 * CloudDrive 2.0 液体玻璃效果
 * 基于iOS 16液体玻璃特性的动态视觉效果
 */

class CloudDriveLiquidEffects {
    constructor() {
        this.liquidOverlay = null;
        this.blobs = [];
        this.mousePosition = { x: 0, y: 0 };
        this.isInitialized = false;
        this.animationId = null;
        
        this.init();
    }

    init() {
        if (this.isInitialized) return;
        
        this.createLiquidOverlay();
        this.createLiquidBlobs();
        this.setupMouseTracking();
        this.setupIntersectionObserver();
        this.startAnimationLoop();
        this.setupPerformanceOptimization();
        
        this.isInitialized = true;
        console.log('CloudDrive 液体效果已初始化');
    }

    createLiquidOverlay() {
        this.liquidOverlay = document.querySelector('.liquid-overlay');
        if (!this.liquidOverlay) {
            this.liquidOverlay = document.createElement('div');
            this.liquidOverlay.className = 'liquid-overlay';
            document.body.appendChild(this.liquidOverlay);
        }
    }

    createLiquidBlobs() {
        const colors = [
            'rgba(64, 123, 255, 0.25)',   // CloudDrive 主色
            'rgba(120, 119, 198, 0.25)',  // 次要色
            'rgba(52, 199, 89, 0.25)',    // 成功色
            'rgba(255, 204, 0, 0.25)',    // 警告色
            'rgba(255, 69, 58, 0.25)'     // 错误色
        ];

        for (let i = 0; i < 5; i++) {
            const blob = document.createElement('div');
            blob.className = 'liquid-blob';
            
            const size = Math.random() * 250 + 150;
            blob.style.width = `${size}px`;
            blob.style.height = `${size}px`;
            blob.style.background = colors[i % colors.length];
            blob.style.left = `${Math.random() * 100}%`;
            blob.style.top = `${Math.random() * 100}%`;
            blob.style.animationDelay = `${Math.random() * 25}s`;
            
            this.liquidOverlay.appendChild(blob);
            this.blobs.push({
                element: blob,
                x: Math.random() * window.innerWidth,
                y: Math.random() * window.innerHeight,
                vx: (Math.random() - 0.5) * 1.5,
                vy: (Math.random() - 0.5) * 1.5,
                size: size,
                originalSize: size,
                targetSize: size
            });
        }
    }

    setupMouseTracking() {
        let mouseTimeout;
        
        document.addEventListener('mousemove', (e) => {
            this.mousePosition.x = e.clientX;
            this.mousePosition.y = e.clientY;
            
            // 清除之前的超时
            clearTimeout(mouseTimeout);
            
            // 鼠标移动时增强效果
            this.updateBlobsBasedOnMouse();
            
            // 鼠标停止移动后恢复正常
            mouseTimeout = setTimeout(() => {
                this.resetBlobSizes();
            }, 1000);
        });
    }

    updateBlobsBasedOnMouse() {
        this.blobs.forEach((blob, index) => {
            const dx = this.mousePosition.x - blob.x;
            const dy = this.mousePosition.y - blob.y;
            const distance = Math.sqrt(dx * dx + dy * dy);
            
            if (distance < 300) {
                const force = (300 - distance) / 300;
                
                // 添加吸引力
                blob.vx += (dx / distance) * force * 0.3;
                blob.vy += (dy / distance) * force * 0.3;
                
                // 增大尺寸
                blob.targetSize = blob.originalSize * (1 + force * 0.3);
            } else {
                // 恢复原始尺寸
                blob.targetSize = blob.originalSize;
            }
        });
    }

    resetBlobSizes() {
        this.blobs.forEach(blob => {
            blob.targetSize = blob.originalSize;
        });
    }

    setupIntersectionObserver() {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.addGlassEffect(entry.target);
                }
            });
        }, {
            threshold: 0.1,
            rootMargin: '50px'
        });

        // 观察所有玻璃元素
        document.querySelectorAll('.glass-panel, .glass-card, .glass-btn').forEach(el => {
            observer.observe(el);
        });
    }

    addGlassEffect(element) {
        if (element.classList.contains('glass-enhanced')) return;
        
        element.classList.add('glass-enhanced');
        
        // 添加悬停效果
        element.addEventListener('mouseenter', (e) => {
            this.createRippleEffect(element, e);
        });
        
        // 添加点击效果
        element.addEventListener('click', (e) => {
            this.createClickEffect(element, e);
        });
        
        // 添加焦点效果
        if (element.tagName === 'BUTTON' || element.tagName === 'INPUT') {
            element.addEventListener('focus', () => {
                this.createFocusEffect(element);
            });
        }
    }

    createRippleEffect(element, event) {
        const ripple = document.createElement('div');
        ripple.className = 'ripple-effect';
        
        const rect = element.getBoundingClientRect();
        const x = event ? event.clientX - rect.left : rect.width / 2;
        const y = event ? event.clientY - rect.top : rect.height / 2;
        
        ripple.style.cssText = `
            position: absolute;
            left: ${x}px;
            top: ${y}px;
            width: 0;
            height: 0;
            background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, transparent 70%);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            animation: rippleExpand 0.8s ease-out;
            pointer-events: none;
            z-index: 10;
        `;
        
        element.style.position = 'relative';
        element.appendChild(ripple);
        
        setTimeout(() => {
            ripple.remove();
        }, 800);
    }

    createClickEffect(element, event) {
        const rect = element.getBoundingClientRect();
        const x = event.clientX - rect.left;
        const y = event.clientY - rect.top;
        
        const clickEffect = document.createElement('div');
        clickEffect.className = 'click-effect';
        clickEffect.style.cssText = `
            position: absolute;
            left: ${x}px;
            top: ${y}px;
            width: 0;
            height: 0;
            background: radial-gradient(circle, rgba(64,123,255,0.6) 0%, transparent 70%);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            animation: clickExpand 0.5s ease-out;
            pointer-events: none;
            z-index: 15;
        `;
        
        element.appendChild(clickEffect);
        
        // 添加震动效果
        element.style.animation = 'clickShake 0.3s ease-out';
        
        setTimeout(() => {
            clickEffect.remove();
            element.style.animation = '';
        }, 500);
    }

    createFocusEffect(element) {
        element.style.boxShadow = '0 0 0 3px rgba(64, 123, 255, 0.3)';
        element.style.transition = 'box-shadow 0.2s ease';
        
        const removeFocus = () => {
            element.style.boxShadow = '';
            element.removeEventListener('blur', removeFocus);
        };
        
        element.addEventListener('blur', removeFocus);
    }

    startAnimationLoop() {
        const animate = () => {
            this.updateBlobs();
            this.animationId = requestAnimationFrame(animate);
        };
        animate();
    }

    updateBlobs() {
        this.blobs.forEach(blob => {
            // 更新位置
            blob.x += blob.vx;
            blob.y += blob.vy;
            
            // 边界反弹
            if (blob.x <= 0 || blob.x >= window.innerWidth) {
                blob.vx *= -0.8;
                blob.x = Math.max(0, Math.min(window.innerWidth, blob.x));
            }
            if (blob.y <= 0 || blob.y >= window.innerHeight) {
                blob.vy *= -0.8;
                blob.y = Math.max(0, Math.min(window.innerHeight, blob.y));
            }
            
            // 应用摩擦力
            blob.vx *= 0.995;
            blob.vy *= 0.995;
            
            // 平滑尺寸变化
            blob.size += (blob.targetSize - blob.size) * 0.1;
            
            // 更新DOM元素
            blob.element.style.transform = `translate(${blob.x - blob.size/2}px, ${blob.y - blob.size/2}px)`;
            blob.element.style.width = `${blob.size}px`;
            blob.element.style.height = `${blob.size}px`;
        });
    }

    setupPerformanceOptimization() {
        // 检测设备性能
        const isLowEnd = navigator.hardwareConcurrency <= 4 || 
                        /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
        
        if (isLowEnd) {
            // 减少blob数量和效果强度
            this.blobs = this.blobs.slice(0, 3);
            document.documentElement.style.setProperty('--blur-medium', 'blur(8px)');
            document.documentElement.style.setProperty('--blur-heavy', 'blur(12px)');
        }
        
        // 页面可见性API优化
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.pauseAnimations();
            } else {
                this.resumeAnimations();
            }
        });
    }

    pauseAnimations() {
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
            this.animationId = null;
        }
        
        this.blobs.forEach(blob => {
            blob.element.style.animationPlayState = 'paused';
        });
    }

    resumeAnimations() {
        if (!this.animationId) {
            this.startAnimationLoop();
        }
        
        this.blobs.forEach(blob => {
            blob.element.style.animationPlayState = 'running';
        });
    }

    // 窗口大小调整处理
    handleResize() {
        this.blobs.forEach(blob => {
            blob.x = Math.min(blob.x, window.innerWidth);
            blob.y = Math.min(blob.y, window.innerHeight);
        });
    }

    // 销毁方法
    destroy() {
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
        }
        
        if (this.liquidOverlay) {
            this.liquidOverlay.remove();
        }
        
        this.blobs = [];
        this.isInitialized = false;
    }
}

// CSS动画定义
const liquidEffectsCSS = `
@keyframes rippleExpand {
    0% {
        width: 0;
        height: 0;
        opacity: 1;
    }
    100% {
        width: 200px;
        height: 200px;
        opacity: 0;
    }
}

@keyframes clickExpand {
    0% {
        width: 0;
        height: 0;
        opacity: 1;
    }
    100% {
        width: 120px;
        height: 120px;
        opacity: 0;
    }
}

@keyframes clickShake {
    0%, 100% {
        transform: translateX(0);
    }
    25% {
        transform: translateX(-2px);
    }
    75% {
        transform: translateX(2px);
    }
}

.glass-enhanced {
    overflow: hidden;
}

.liquid-blob {
    transition: transform 0.1s ease-out, width 0.3s ease, height 0.3s ease;
    will-change: transform, width, height;
}

/* 性能优化 */
@media (max-width: 768px) {
    .liquid-blob {
        filter: blur(30px) !important;
    }
}

@media (prefers-reduced-motion: reduce) {
    .liquid-blob,
    .ripple-effect,
    .click-effect {
        animation: none !important;
        transition: none !important;
    }
}
`;

// 注入CSS
const style = document.createElement('style');
style.textContent = liquidEffectsCSS;
document.head.appendChild(style);

// 初始化液体效果
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.cloudDriveLiquidEffects = new CloudDriveLiquidEffects();
    });
} else {
    window.cloudDriveLiquidEffects = new CloudDriveLiquidEffects();
}

// 处理窗口大小调整
window.addEventListener('resize', () => {
    if (window.cloudDriveLiquidEffects) {
        window.cloudDriveLiquidEffects.handleResize();
    }
});

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CloudDriveLiquidEffects;
}
